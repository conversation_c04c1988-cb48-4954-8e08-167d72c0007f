@echo off
chcp 65001 >nul
title 星盘AI分析H5页面启动

echo 🌟 星盘AI分析H5页面启动脚本
echo ================================

REM 检查是否在正确的目录
if not exist "index.html" (
    echo ❌ 错误: 请在h5目录下运行此脚本
    pause
    exit /b 1
)

echo 📁 当前目录: %CD%
echo 📋 文件列表:
dir /b

echo.
echo 🚀 启动选项:
echo 1. 在默认浏览器中打开页面
echo 2. 启动简单HTTP服务器 (Python)
echo 3. 仅显示文件路径
echo 4. 运行连接测试

set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo 🌐 在浏览器中打开页面...
    start index.html
    goto end
)

if "%choice%"=="2" (
    echo 🐍 启动Python HTTP服务器...
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 未找到Python，请安装Python或选择其他选项
        goto end
    )
    echo 服务器地址: http://localhost:8000
    python -m http.server 8000
    goto end
)

if "%choice%"=="3" (
    echo 📍 文件路径:
    echo 主页面: file:///%CD:\=/%/index.html
    echo 测试页面: file:///%CD:\=/%/test.html
    echo.
    echo 💡 提示: 直接在浏览器中打开这些文件即可使用
    goto end
)

if "%choice%"=="4" (
    echo 🧪 运行连接测试...
    start test.html
    goto end
)

echo ❌ 无效选择

:end
echo.
echo 📝 使用说明:
echo 1. 确保后端服务运行在 http://localhost:8082
echo 2. 检查token是否有效
echo 3. 如遇到CORS问题，请使用HTTP服务器而非直接打开文件
echo 4. 建议先运行连接测试确认API可用性
echo.
echo ✨ 享受星盘AI分析的打字机效果吧！
pause
