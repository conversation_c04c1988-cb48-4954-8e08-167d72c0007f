<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API连接测试</h1>
        <p>测试与后端API的连接状态</p>
        
        <div>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <button class="btn" onclick="testSSE()">测试SSE流</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:8082/birthday/astrolabe/chart/analysis';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwyAMANC7eGawAUPgBt17AROMGrUlUT5SpKp3b_Wrut76vK6z0_tlyK5oYTQRMaxepZQhmCxFalNmaO_ip4LZOccoreW8fsDAAD__w.K8eg4DnDFjgVSlRGaQnEouh9iNM9qF-g0yWDyHiJtWQ';
        
        const REQUEST_DATA = {
            "chartType": 2,
            "planets": [0,1,2,3,4,5,6,7,8,9,"m","H"],
            "planet_xs": [],
            "virtual": [10,"pFortune"],
            "h_sys": "p",
            "svgType": "1",
            "constellationId": 122,
            "type": 0
        };

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function setStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.className = `status ${type}`;
            statusElement.textContent = message;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').textContent = '';
        }

        async function testConnection() {
            log('开始测试基本连接...');
            setStatus('测试中...', 'info');
            
            try {
                const response = await fetch('http://localhost:8082/birthday/system/carousel', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    log('✅ 基本连接成功');
                    log(`响应状态: ${response.status}`);
                    log(`响应数据长度: ${data.length} 字符`);
                    setStatus('基本连接成功', 'success');
                } else {
                    log(`❌ 连接失败: ${response.status} ${response.statusText}`);
                    setStatus('基本连接失败', 'error');
                }
            } catch (error) {
                log(`❌ 连接错误: ${error.message}`);
                setStatus('连接错误', 'error');
            }
        }

        async function testSSE() {
            log('开始测试SSE连接...');
            setStatus('测试SSE中...', 'info');
            
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${TOKEN}`,
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(REQUEST_DATA)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                log('✅ SSE连接建立成功');
                setStatus('SSE连接成功，正在接收数据...', 'success');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let dataReceived = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        log('📡 SSE流结束');
                        setStatus(`SSE测试完成，共接收 ${dataReceived} 字符`, 'success');
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    dataReceived += chunk.length;
                    
                    // 处理完整的SSE消息
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.trim().startsWith('data: ')) {
                            const data = line.substring(6);
                            if (data && data !== '') {
                                log(`📨 接收数据: ${data.substring(0, 50)}${data.length > 50 ? '...' : ''}`);
                            }
                        }
                    }
                    
                    // 限制测试时间，避免无限接收
                    if (dataReceived > 1000) {
                        log('📊 已接收足够数据，停止测试');
                        reader.cancel();
                        break;
                    }
                }
                
            } catch (error) {
                log(`❌ SSE测试失败: ${error.message}`);
                setStatus('SSE测试失败', 'error');
            }
        }

        // 页面加载时显示初始状态
        window.onload = function() {
            log('🚀 测试页面已加载');
            log(`API地址: ${API_URL}`);
            log('点击按钮开始测试...');
        };
    </script>
</body>
</html>
