/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 控制面板样式 */
.control-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #ff9800, #f57c00);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.btn-danger {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator span {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-connected {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 2px solid #4CAF50;
}

.status-disconnected {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 2px solid #f44336;
}

.status-connecting {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
    border: 2px solid #ff9800;
}

/* 内容区域 */
.content-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .content-area {
        grid-template-columns: 1fr;
    }
}

/* 打字机容器 */
.typewriter-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    position: relative;
    min-height: 400px;
}

.typewriter-container::before {
    content: '💫 实时输出';
    position: absolute;
    top: -10px;
    left: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.typewriter-content {
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
    min-height: 350px;
}

/* 光标动画 */
.cursor {
    display: inline-block;
    background-color: #333;
    width: 2px;
    animation: blink 1s infinite;
    margin-left: 2px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Markdown 预览 */
.markdown-preview {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    position: relative;
    min-height: 400px;
}

.markdown-preview::before {
    content: '📖 Markdown 预览';
    position: absolute;
    top: -10px;
    left: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.markdown-preview h3 {
    margin-bottom: 20px;
    color: #333;
    opacity: 0;
}

.markdown-content {
    font-size: 1rem;
    line-height: 1.6;
    color: #333;
    max-height: 350px;
    overflow-y: auto;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.markdown-content p {
    margin-bottom: 15px;
}

.markdown-content ul, .markdown-content ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.markdown-content code {
    background: #f4f4f4;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.markdown-content pre {
    background: #f4f4f4;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 15px;
}

/* 进度信息 */
.progress-info {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.stats {
    display: flex;
    justify-content: space-around;
    gap: 20px;
    flex-wrap: wrap;
}

.stats span {
    font-weight: 600;
    color: #333;
}

/* 错误消息 */
.error-message {
    background: rgba(244, 67, 54, 0.1);
    border: 2px solid #f44336;
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #d32f2f;
    font-weight: 600;
}

.error-icon {
    font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .control-panel {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .stats {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
