<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星盘AI分析 - 打字机效果</title>
    <link rel="stylesheet" href="style.css">
    <!-- 引入 marked.js 用于 markdown 渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- 引入 highlight.js 用于代码高亮 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🌟 星盘AI分析</h1>
            <p class="subtitle">实时生成个性化星盘解读</p>
        </header>

        <div class="control-panel">
            <button id="startAnalysis" class="btn btn-primary">开始分析</button>
            <button id="stopAnalysis" class="btn btn-secondary" disabled>停止分析</button>
            <button id="clearContent" class="btn btn-danger">清空内容</button>
            <div class="status-indicator">
                <span id="connectionStatus" class="status-disconnected">未连接</span>
            </div>
        </div>

        <div class="content-area">
            <div class="typewriter-container">
                <div id="typewriterContent" class="typewriter-content"></div>
                <span id="cursor" class="cursor">|</span>
            </div>
            
            <div class="markdown-preview">
                <h3>Markdown 预览</h3>
                <div id="markdownContent" class="markdown-content"></div>
            </div>
        </div>

        <div class="progress-info">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div class="stats">
                <span>字符数: <span id="charCount">0</span></span>
                <span>用时: <span id="duration">0</span>s</span>
                <span>速度: <span id="speed">0</span> 字符/秒</span>
            </div>
        </div>

        <div class="error-message" id="errorMessage" style="display: none;">
            <span class="error-icon">⚠️</span>
            <span id="errorText"></span>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
