# 星盘AI分析 - 打字机效果页面

这是一个使用打字机效果实时渲染星盘AI分析结果的H5页面。

## 功能特性

- 🌟 **实时数据流**: 通过SSE (Server-Sent Events) 接收后端实时数据
- ⌨️ **打字机效果**: 模拟打字机逐字显示分析结果
- 📖 **Markdown渲染**: 实时将Markdown格式转换为HTML显示
- 📊 **进度统计**: 显示字符数、用时、速度等统计信息
- 🎨 **美观界面**: 现代化的渐变背景和卡片式布局
- 📱 **响应式设计**: 支持移动端和桌面端

## 文件结构

```text
h5/
├── index.html      # 主页面 - 星盘AI分析打字机效果页面
├── style.css       # 样式文件 - 现代化UI设计
├── script.js       # JavaScript逻辑 - SSE连接和打字机效果
├── test.html       # 测试页面 - API连接测试工具
├── start.sh        # Linux/Mac启动脚本
├── start.bat       # Windows启动脚本
└── README.md       # 说明文档
```

## 快速启动

### 方式一：使用启动脚本

- **Linux/Mac**: 运行 `./start.sh`
- **Windows**: 双击 `start.bat`

### 方式二：手动启动

1. 确保后端服务运行在 `http://localhost:8082`
2. 打开 `index.html` 文件
3. 点击"开始分析"按钮
4. 观看实时的打字机效果和Markdown渲染

### 方式三：连接测试

打开 `test.html` 进行API连接测试

## API配置

页面默认配置：

- **接口地址**: `http://localhost:8082/birthday/astrolabe/chart/analysis`
- **请求方法**: POST
- **认证**: Bearer Token
- **请求参数**: 固定的星盘分析参数

## 技术实现

### 前端技术

- 原生JavaScript (ES6+)
- Fetch API 处理SSE连接
- marked.js 进行Markdown解析
- highlight.js 代码高亮
- CSS3 动画和渐变

### 后端集成

- 兼容Spring Boot SSE实现
- 处理流式数据传输
- 支持缓存和多客户端订阅

## 自定义配置

如需修改配置，请编辑 `script.js` 文件中的以下参数：

```javascript
// API 配置
this.apiUrl = 'http://localhost:8082/birthday/astrolabe/chart/analysis';
this.token = 'your-token-here';

// 打字机速度 (毫秒)
this.typewriterSpeed = 30;

// 请求参数
this.requestData = {
    "chartType": 2,
    "planets": [0,1,2,3,4,5,6,7,8,9,"m","H"],
    // ... 其他参数
};
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 后端修改说明

为了支持H5页面的跨域访问，已对后端进行了以下修改：

### CORS配置优化

文件：`src/main/java/com/lhx/birthday/config/CorsConfig.java`

```java
// 为H5页面添加特殊的CORS配置
registry.addMapping("/astrolabe/chart/analysis")
        .allowedOrigins("*")
        .allowedHeaders("*")
        .allowCredentials(false)
        .allowedMethods("GET", "POST", "OPTIONS")
        .maxAge(3600);
```

这个修改确保了：

- 允许所有来源访问分析接口
- 支持文件协议和本地服务器
- 正确处理OPTIONS预检请求

## 注意事项

1. 确保后端CORS配置正确
2. Token需要有效且未过期
3. 网络连接稳定以保证SSE正常工作
4. 建议在现代浏览器中使用以获得最佳体验
5. 如遇到CORS问题，建议使用HTTP服务器而非直接打开文件
