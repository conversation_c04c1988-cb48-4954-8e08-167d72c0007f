#!/bin/bash

# 星盘AI分析H5页面启动脚本

echo "🌟 星盘AI分析H5页面启动脚本"
echo "================================"

# 检查是否在正确的目录
if [ ! -f "index.html" ]; then
    echo "❌ 错误: 请在h5目录下运行此脚本"
    exit 1
fi

echo "📁 当前目录: $(pwd)"
echo "📋 文件列表:"
ls -la

echo ""
echo "🚀 启动选项:"
echo "1. 在默认浏览器中打开页面"
echo "2. 启动简单HTTP服务器 (Python)"
echo "3. 启动简单HTTP服务器 (Node.js)"
echo "4. 仅显示文件路径"
echo "5. 运行连接测试"

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🌐 在浏览器中打开页面..."
        if command -v open >/dev/null 2>&1; then
            open index.html
        elif command -v xdg-open >/dev/null 2>&1; then
            xdg-open index.html
        elif command -v start >/dev/null 2>&1; then
            start index.html
        else
            echo "❌ 无法自动打开浏览器，请手动打开 index.html"
        fi
        ;;
    2)
        echo "🐍 启动Python HTTP服务器..."
        if command -v python3 >/dev/null 2>&1; then
            echo "服务器地址: http://localhost:8000"
            python3 -m http.server 8000
        elif command -v python >/dev/null 2>&1; then
            echo "服务器地址: http://localhost:8000"
            python -m SimpleHTTPServer 8000
        else
            echo "❌ 未找到Python，请安装Python或选择其他选项"
        fi
        ;;
    3)
        echo "📦 启动Node.js HTTP服务器..."
        if command -v npx >/dev/null 2>&1; then
            echo "服务器地址: http://localhost:8000"
            npx http-server -p 8000 -c-1
        else
            echo "❌ 未找到npx，请安装Node.js或选择其他选项"
        fi
        ;;
    4)
        echo "📍 文件路径:"
        echo "主页面: file://$(pwd)/index.html"
        echo "测试页面: file://$(pwd)/test.html"
        echo ""
        echo "💡 提示: 直接在浏览器中打开这些文件即可使用"
        ;;
    5)
        echo "🧪 运行连接测试..."
        if command -v open >/dev/null 2>&1; then
            open test.html
        elif command -v xdg-open >/dev/null 2>&1; then
            xdg-open test.html
        elif command -v start >/dev/null 2>&1; then
            start test.html
        else
            echo "请手动打开 test.html 进行连接测试"
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "📝 使用说明:"
echo "1. 确保后端服务运行在 http://localhost:8082"
echo "2. 检查token是否有效"
echo "3. 如遇到CORS问题，请使用HTTP服务器而非直接打开文件"
echo "4. 建议先运行连接测试确认API可用性"
echo ""
echo "✨ 享受星盘AI分析的打字机效果吧！"
