class TypewriterSSE {
    constructor() {
        this.eventSource = null;
        this.isConnected = false;
        this.isAnalyzing = false;
        this.typewriterSpeed = 30; // 打字机速度 (毫秒)
        this.currentContent = '';
        this.displayedContent = '';
        this.typewriterQueue = [];
        this.typewriterTimer = null;
        this.startTime = null;
        this.charCount = 0;
        
        // API 配置
        this.apiUrl = 'http://localhost:8082/birthday/astrolabe/chart/analysis';
        this.token = 'eyJhbGciOiJIUzI1NiIsInppcCI6IkRFRiJ9.eNpkyzsOwyAMANC7eGawAUPgBt17AROMGrUlUT5SpKp3b_Wrut76vK6z0_tlyK5oYTQRMaxepZQhmCxFalNmaO_ip4LZOccoreW8fsDAAD__w.K8eg4DnDFjgVSlRGaQnEouh9iNM9qF-g0yWDyHiJtWQ';
        
        // 固定请求参数
        this.requestData = {
            "chartType": 2,
            "planets": [0,1,2,3,4,5,6,7,8,9,"m","H"],
            "planet_xs": [],
            "virtual": [10,"pFortune"],
            "h_sys": "p",
            "svgType": "1",
            "constellationId": 122,
            "type": 0
        };
        
        this.initializeElements();
        this.bindEvents();
        this.updateStats();
    }
    
    initializeElements() {
        this.startBtn = document.getElementById('startAnalysis');
        this.stopBtn = document.getElementById('stopAnalysis');
        this.clearBtn = document.getElementById('clearContent');
        this.statusElement = document.getElementById('connectionStatus');
        this.typewriterContent = document.getElementById('typewriterContent');
        this.markdownContent = document.getElementById('markdownContent');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.charCountElement = document.getElementById('charCount');
        this.durationElement = document.getElementById('duration');
        this.speedElement = document.getElementById('speed');
        this.progressFill = document.getElementById('progressFill');
        this.cursor = document.getElementById('cursor');
    }
    
    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startAnalysis());
        this.stopBtn.addEventListener('click', () => this.stopAnalysis());
        this.clearBtn.addEventListener('click', () => this.clearContent());
    }
    
    updateStatus(status, message) {
        this.statusElement.className = `status-${status}`;
        this.statusElement.textContent = message;
    }
    
    showError(message) {
        this.errorText.textContent = message;
        this.errorMessage.style.display = 'flex';
        setTimeout(() => {
            this.errorMessage.style.display = 'none';
        }, 5000);
    }
    
    clearContent() {
        this.currentContent = '';
        this.displayedContent = '';
        this.typewriterQueue = [];
        this.charCount = 0;
        this.typewriterContent.textContent = '';
        this.markdownContent.innerHTML = '';
        this.progressFill.style.width = '0%';
        this.updateStats();
        
        if (this.typewriterTimer) {
            clearInterval(this.typewriterTimer);
            this.typewriterTimer = null;
        }
    }
    
    async startAnalysis() {
        if (this.isAnalyzing) return;

        this.isAnalyzing = true;
        this.startBtn.disabled = true;
        this.stopBtn.disabled = false;
        this.startTime = Date.now();
        this.clearContent();

        this.updateStatus('connecting', '连接中...');

        try {
            // 使用 EventSource 来处理 SSE 连接
            // 但是 EventSource 不支持 POST 请求，所以我们需要使用 fetch
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`,
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(this.requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.updateStatus('connected', '已连接');
            this.isConnected = true;

            // 读取流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (this.isAnalyzing) {
                const { done, value } = await reader.read();

                if (done) {
                    console.log('Stream completed');
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                buffer += chunk;

                // 处理完整的 SSE 消息
                const lines = buffer.split('\n');
                buffer = lines.pop() || ''; // 保留最后一个不完整的行

                for (const line of lines) {
                    this.processSSELine(line);
                }
            }

            this.onAnalysisComplete();

        } catch (error) {
            console.error('Connection error:', error);
            this.showError(`连接失败: ${error.message}`);
            this.updateStatus('disconnected', '连接失败');
            this.onAnalysisComplete();
        }
    }
    
    processSSELine(line) {
        line = line.trim();

        if (line.startsWith('data: ')) {
            const data = line.substring(6);

            // 检查是否是结束标记
            if (data === '[DONE]' || data === '') {
                console.log('Analysis completed or empty data');
                return;
            }

            // 检查是否是错误消息
            if (line.includes('event: error')) {
                this.showError(`服务器错误: ${data}`);
                return;
            }

            // 直接将数据作为文本内容处理（后端发送的是纯文本）
            if (data && data !== '') {
                this.addToTypewriter(data);
            }
        } else if (line.startsWith('event: ')) {
            // 处理事件类型
            const eventType = line.substring(7);
            if (eventType === 'error') {
                console.log('Error event detected');
            }
        }
    }
    
    addToTypewriter(text) {
        if (!text) return;

        this.currentContent += text;

        // 将新文本添加到打字机队列
        for (let char of text) {
            this.typewriterQueue.push(char);
        }

        // 如果打字机没有运行，启动它
        if (!this.typewriterTimer) {
            this.startTypewriter();
        }

        // 实时更新 Markdown 预览
        this.updateMarkdownPreview();

        console.log(`Added ${text.length} characters to typewriter queue`);
    }
    
    startTypewriter() {
        this.typewriterTimer = setInterval(() => {
            if (this.typewriterQueue.length > 0) {
                const char = this.typewriterQueue.shift();
                this.displayedContent += char;
                this.typewriterContent.textContent = this.displayedContent;
                this.charCount++;
                this.updateStats();
                
                // 自动滚动到底部
                this.typewriterContent.scrollTop = this.typewriterContent.scrollHeight;
            } else if (!this.isAnalyzing) {
                // 如果队列为空且分析已完成，停止打字机
                clearInterval(this.typewriterTimer);
                this.typewriterTimer = null;
                this.cursor.style.display = 'none';
            }
        }, this.typewriterSpeed);
    }
    
    updateMarkdownPreview() {
        if (typeof marked !== 'undefined') {
            try {
                const html = marked.parse(this.currentContent);
                this.markdownContent.innerHTML = html;
                
                // 代码高亮
                if (typeof hljs !== 'undefined') {
                    this.markdownContent.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                }
                
                // 自动滚动到底部
                this.markdownContent.scrollTop = this.markdownContent.scrollHeight;
            } catch (error) {
                console.error('Markdown parsing error:', error);
            }
        }
    }
    
    updateStats() {
        this.charCountElement.textContent = this.charCount;
        
        if (this.startTime) {
            const duration = Math.floor((Date.now() - this.startTime) / 1000);
            this.durationElement.textContent = duration;
            
            const speed = duration > 0 ? Math.round(this.charCount / duration) : 0;
            this.speedElement.textContent = speed;
            
            // 更新进度条（基于字符数，假设完整分析大约2000字符）
            const progress = Math.min((this.charCount / 2000) * 100, 100);
            this.progressFill.style.width = `${progress}%`;
        }
    }
    
    stopAnalysis() {
        this.isAnalyzing = false;
        this.isConnected = false;
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
        this.updateStatus('disconnected', '已断开');

        // 继续完成打字机效果
        if (this.typewriterQueue.length > 0 && !this.typewriterTimer) {
            this.startTypewriter();
        }

        console.log('Analysis stopped by user');
    }
    
    onAnalysisComplete() {
        this.isAnalyzing = false;
        this.isConnected = false;
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
        this.updateStatus('disconnected', '分析完成');

        // 确保所有内容都显示完毕
        if (this.typewriterQueue.length === 0 && this.typewriterTimer) {
            clearInterval(this.typewriterTimer);
            this.typewriterTimer = null;
            this.cursor.style.display = 'none';
        }

        // 最终更新统计信息
        this.updateStats();

        console.log('Analysis completed. Total characters:', this.charCount);
        console.log('Final content length:', this.currentContent.length);
        console.log('Displayed content length:', this.displayedContent.length);
        console.log('Queue remaining:', this.typewriterQueue.length);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const typewriterSSE = new TypewriterSSE();
    
    // 添加一些提示信息
    console.log('星盘AI分析页面已加载');
    console.log('点击"开始分析"按钮开始获取实时分析结果');
});
