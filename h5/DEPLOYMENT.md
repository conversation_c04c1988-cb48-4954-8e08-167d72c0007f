# 部署和使用指南

## 🚀 快速开始

### 1. 检查后端服务

确保后端服务正在运行：

```bash
curl http://localhost:8082/birthday/system/carousel
```

如果返回200状态码，说明服务正常。

### 2. 启动H5页面

#### 方法一：使用启动脚本（推荐）

**Linux/Mac:**
```bash
cd h5
./start.sh
```

**Windows:**
```cmd
cd h5
start.bat
```

#### 方法二：直接打开文件

直接在浏览器中打开 `h5/index.html`

#### 方法三：使用HTTP服务器

```bash
# Python 3
cd h5
python3 -m http.server 8000

# Python 2
cd h5
python -m SimpleHTTPServer 8000

# Node.js
cd h5
npx http-server -p 8000 -c-1
```

然后访问 `http://localhost:8000`

### 3. 测试连接

1. 打开 `h5/test.html`
2. 点击"测试连接"按钮
3. 点击"测试SSE流"按钮
4. 观察日志输出

## 🔧 配置说明

### Token配置

如果需要更新Token，请修改 `script.js` 中的 `token` 变量：

```javascript
this.token = 'your-new-token-here';
```

### API地址配置

如果后端地址不同，请修改 `script.js` 中的 `apiUrl`：

```javascript
this.apiUrl = 'http://your-backend:port/birthday/astrolabe/chart/analysis';
```

### 请求参数配置

如果需要修改请求参数，请编辑 `script.js` 中的 `requestData`：

```javascript
this.requestData = {
    "chartType": 2,
    "constellationId": 122,
    // ... 其他参数
};
```

## 🐛 故障排除

### 问题1：CORS错误

**症状**: 浏览器控制台显示CORS相关错误

**解决方案**:
1. 使用HTTP服务器而非直接打开文件
2. 确保后端CORS配置正确
3. 检查是否使用了正确的协议（http/https）

### 问题2：连接失败

**症状**: 无法连接到后端API

**解决方案**:
1. 检查后端服务是否运行
2. 验证API地址是否正确
3. 检查网络连接
4. 验证Token是否有效

### 问题3：SSE连接中断

**症状**: 数据流中途停止

**解决方案**:
1. 检查网络稳定性
2. 确认后端服务正常
3. 查看浏览器控制台错误信息
4. 尝试刷新页面重新连接

### 问题4：打字机效果异常

**症状**: 文字显示不正常或速度异常

**解决方案**:
1. 检查JavaScript控制台错误
2. 调整打字机速度参数
3. 清空内容后重新开始

## 📊 性能优化

### 1. 打字机速度调整

修改 `script.js` 中的速度参数：

```javascript
this.typewriterSpeed = 30; // 毫秒，数值越小速度越快
```

### 2. 缓存优化

页面会自动处理以下缓存：
- Markdown渲染结果
- 统计信息
- 显示状态

### 3. 内存管理

页面会自动清理：
- 定时器
- 事件监听器
- SSE连接

## 🔍 调试模式

### 启用详细日志

在浏览器控制台中设置：

```javascript
// 启用详细日志
localStorage.setItem('debug', 'true');

// 禁用详细日志
localStorage.removeItem('debug');
```

### 查看实时状态

在控制台中查看当前状态：

```javascript
// 查看当前内容长度
console.log('Current content:', typewriterSSE.currentContent.length);

// 查看队列状态
console.log('Queue length:', typewriterSSE.typewriterQueue.length);

// 查看连接状态
console.log('Is analyzing:', typewriterSSE.isAnalyzing);
```

## 📱 移动端适配

页面已针对移动端进行优化：

- 响应式布局
- 触摸友好的按钮
- 适配小屏幕显示
- 优化滚动体验

## 🌐 浏览器兼容性

**推荐浏览器**:
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

**不支持的浏览器**:
- Internet Explorer
- 过旧版本的移动浏览器

## 📈 监控和分析

页面提供以下实时统计：

- 字符数统计
- 传输速度
- 连接时长
- 进度百分比

这些数据可用于：
- 性能分析
- 用户体验优化
- 问题诊断

## 🔒 安全注意事项

1. **Token安全**: 不要在生产环境中硬编码Token
2. **HTTPS**: 生产环境建议使用HTTPS
3. **输入验证**: 后端应验证所有输入参数
4. **访问控制**: 确保适当的用户权限验证

## 📞 技术支持

如遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 验证后端服务日志
4. 使用测试页面进行诊断

---

**祝您使用愉快！** 🌟
