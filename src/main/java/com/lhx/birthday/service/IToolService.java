package com.lhx.birthday.service;


import com.lhx.birthday.request.horoscope.JieqiRequest;
import com.lhx.birthday.request.horoscope.LaohuangliRequest;
import com.lhx.birthday.request.horoscope.ZeshiRequest;
import com.lhx.birthday.vo.Result;

/**
 * @Description: 反馈
 * @author: lhx
 */
public interface IToolService {

    Result LaohuangliHoroscope(LaohuangliRequest laohuangliRequest);

    Result LaohuangliList();

    Result ZeshiHoroscope(ZeshiRequest zeshiRequest);

    Result JieqiHoroscope(JieqiRequest jieqiRequest);

}
