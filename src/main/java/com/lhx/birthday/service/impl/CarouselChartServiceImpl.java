package com.lhx.birthday.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lhx.birthday.entity.Attachment;
import com.lhx.birthday.entity.CarouselChart;
import com.lhx.birthday.enums.DeleteFlag;
import com.lhx.birthday.mapper.CarouselChartMapper;
import com.lhx.birthday.request.carouselchart.CarouselChartBaseRequest;
import com.lhx.birthday.request.carouselchart.CarouselChartRequest;
import com.lhx.birthday.response.carouselchart.CarouselChartResponse;
import com.lhx.birthday.service.ICarouselChartService;
import com.lhx.birthday.vo.carouselchart.CarouselChartVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 发现模块服务
 */
@Slf4j
@Service
public class CarouselChartServiceImpl implements ICarouselChartService {

    @Autowired
    private CarouselChartMapper carouselChartMapper;

    @Override
    public CarouselChartResponse getList(CarouselChartBaseRequest carouselChartBaseRequest) {
        Sort sort = Sort.by(Sort.Order.desc("sort"));
        List<CarouselChart> carouselCharts = carouselChartMapper.findAll(this.build(carouselChartBaseRequest),sort);

        // 内存筛选
        List<CarouselChart> filteredCarouselCharts = carouselCharts.stream()
                .filter(chart -> isTargetMatch(chart, carouselChartBaseRequest))
                .collect(Collectors.toList());

        List<CarouselChartVO> carouselChartVOList = filteredCarouselCharts.stream().map(carouselChart -> CarouselChartVO.builder()
                .link(carouselChart.getLink())
                .type(carouselChart.getType())
                .bannerUrl(carouselChart.getBannerUrl())
                .param(carouselChart.getParam())
                .build()).collect(Collectors.toList());
        return CarouselChartResponse.builder()
                .carouselCharts(carouselChartVOList)
                .build();
    }

    private boolean isTargetMatch(CarouselChart chart, CarouselChartBaseRequest request) {
        String targetingJson = chart.getTargeting();

        // 如果 targeting 字段为空或格式不正确，则默认显示
        if (targetingJson == null || !JSONUtil.isJsonArray(targetingJson)) {
            return true;
        }

        JSONArray targetArray = JSONUtil.parseArray(targetingJson);
        // 如果 targeting 数组为空，也默认显示
        if (targetArray.isEmpty()) {
            return true;
        }

        // 检查是否存在任何一个匹配的规则
        for (Object obj : targetArray) {
            if (obj instanceof JSONObject) {
                JSONObject rule = (JSONObject) obj;
                String requiredChannel = rule.getStr("channel");
                Integer requiredVersion = rule.getInt("version");

                boolean channelMatch = (requiredChannel == null || requiredChannel.isEmpty() || requiredChannel.equals(request.getChannel()));
                boolean versionMatch = (requiredVersion == null || (request.getVersion() != null && request.getVersion() <= requiredVersion));

                if (channelMatch && versionMatch) {
                    return true; // 找到匹配的规则
                }
            }
        }

        return false; // 没有找到任何匹配的规则
    }

    private Specification<CarouselChart> build(CarouselChartBaseRequest carouselChartBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
//            if(carouselChartBaseRequest.getVipState()!=null) {
//                predicates.add(cbuild.equal(root.get("vipState"), carouselChartBaseRequest.getVipState().toValue()));
//            }
            if(carouselChartBaseRequest.getIosState()!=null) {
                predicates.add(cbuild.equal(root.get("iosState"), carouselChartBaseRequest.getIosState().toValue()));
            }
            if(carouselChartBaseRequest.getAndroidState()!=null) {
                predicates.add(cbuild.equal(root.get("androidState"), carouselChartBaseRequest.getAndroidState().toValue()));
            }
            if(carouselChartBaseRequest.getLoginState()!=null) {
                predicates.add(cbuild.equal(root.get("loginState"), carouselChartBaseRequest.getLoginState().toValue()));
            }

            // 删除状态位
            predicates.add(cbuild.equal(root.get("delFlag"), DeleteFlag.NO.toValue()));
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }
}
