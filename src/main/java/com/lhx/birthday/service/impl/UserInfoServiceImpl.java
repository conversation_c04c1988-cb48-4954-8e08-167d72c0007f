package com.lhx.birthday.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.entity.*;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.mapper.*;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.request.userinfo.*;
import com.lhx.birthday.service.IJPushService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.IOSToeknUtils;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.UserPushStrategyVO;
import com.xkzhangsan.time.LunarDate;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalUnit;
import java.util.*;

import static com.lhx.birthday.constant.SettingConstant.*;
import static com.lhx.birthday.constant.SystemConfigConstant.*;
import static com.lhx.birthday.service.impl.ProfileServiceImpl.calculateNextBirthday;
import static com.lhx.birthday.service.impl.ProfileServiceImpl.calculateNextLunarBirthday;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
@Slf4j
public class UserInfoServiceImpl implements IUserInfoService {

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Value("${oss.staticDomain}")
    private String domain;

    @Autowired
    private IJPushService jushService;

    @Autowired
    private ProfilePromptMapper profilePromptMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public UserInfoVO getByUserId(Long userId) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if(userInfoOptional.isPresent()){
            UserInfo userInfo = userInfoOptional.get();
            // 已注销后返回状态位
            if(userInfo.getState().equals(USER_CANCELED)){
                return null;
            }
            UserInfoVO userInfoVO = new UserInfoVO();
            // 获取用户推送配置
            BeanUtils.copyProperties(userInfo ,userInfoVO);
            if(Objects.nonNull(userInfoVO.getZodiacType())){
                userInfoVO.setZodiacName(userInfoVO.getZodiacType().getValue());
            }
            if(Objects.nonNull(userInfoVO.getZodiacSignType())){
                userInfoVO.setZodiacSignName(userInfoVO.getZodiacSignType().getValue());
            }
            // 计算注册天数
            Duration duration = Duration.between(userInfo.getCreateDate(), LocalDateTime.now());
            long regDay = duration.toDays() + (duration.isNegative() ? 0 : 1);
            userInfoVO.setRegDay(regDay);
            // 设置会员信息
            this.checkVipExpiryDate(userInfoVO);

            // 设置老黄历
            userInfoVO.setAlmanacState(DefaultFlag.NO);
            SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, LAOHUANGLI_CONFIG_TYPE);
            JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
            Integer freeDay = jsonObject.getInteger("freeDay");
            long day = ChronoUnit.DAYS.between(userInfo.getCreateDate(), LocalDateTime.now());
            if(day<=freeDay || userInfoVO.getVip()==1){
                userInfoVO.setAlmanacState(DefaultFlag.YES);
            }

            return userInfoVO;
        }
        return null;
    }

    @Override
    public UserInfoVO getByPhone(String phone) {
        UserInfoVO userInfoVO = new UserInfoVO();

        // 获取用户推送配置
        UserInfo userInfo = userInfoMapper.findByPhone(phone);
        BeanUtils.copyProperties(userInfo ,userInfoVO);
        return userInfoVO;
    }

    @Override
    public int updateUserInfo(Long userId, UserUpdateRequest userUpdateRequest) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if(userInfoOptional.isPresent()){
            UserInfo userInfo = userInfoOptional.get();
            
            // 记录是否需要更新推送相关的Redis数据
            boolean horoscopeChanged = false;
            Integer oldHoroscopeRemind = userInfo.getHoroscopeRemind();
            String oldRemindTime = userInfo.getRemindTime();
            
            boolean retrogradeWarningChanged = false;
            Integer oldRetrogradeWarning = userInfo.getRetrogradeWarning();
            
            boolean planetChangeChanged = false;
            Integer oldPlanetChange = userInfo.getPlanetChange();
            
            boolean moonPhaseChanged = false;
            Integer oldMoonPhase = userInfo.getMoonPhase();
            
            boolean eclipseChanged = false;
            Integer oldEclipse = userInfo.getEclipse();
            
            boolean moonVoidChanged = false;
            Integer oldMoonVoid = userInfo.getMoonVoid();
            
            boolean planetAspectChanged = false;
            Integer oldPlanetAspect = userInfo.getPlanetAspect();
            
            // 记录星座类型变化
            boolean zodiacSignTypeChanged = false;
            ZodiacSignType oldZodiacSignType = userInfo.getZodiacSignType();
            
            if(Objects.nonNull(userUpdateRequest.getAvatar())){
                userInfo.setAvatar(userUpdateRequest.getAvatar());
            }
            if(Objects.nonNull(userUpdateRequest.getNickName())){
                userInfo.setNickname(userUpdateRequest.getNickName());
            }
            if(Objects.nonNull(userUpdateRequest.getRealName())){
                userInfo.setRealName(userUpdateRequest.getRealName());
            }
            if(Objects.nonNull(userUpdateRequest.getBirthDate())){
                userInfo.setBirthDate(userUpdateRequest.getBirthDate());
            }
            if(Objects.nonNull(userUpdateRequest.getRegistrationId())){
                registerPush(userInfo,userUpdateRequest.getRegistrationId());
            }
            
            // 更新星座类型
            if(Objects.nonNull(userUpdateRequest.getZodiacSignType())){
                ZodiacSignType newZodiacSignType = ZodiacSignType.fromValue(userUpdateRequest.getZodiacSignType());
                if (!Objects.equals(userInfo.getZodiacSignType(), newZodiacSignType)) {
                    zodiacSignTypeChanged = true;
                    userInfo.setZodiacSignType(newZodiacSignType);
                }
            }
            
            // 更新推送通知相关字段
            if(Objects.nonNull(userUpdateRequest.getHoroscopeRemind())){
                if (!Objects.equals(userInfo.getHoroscopeRemind(), userUpdateRequest.getHoroscopeRemind())) {
                    horoscopeChanged = true;
                }
                userInfo.setHoroscopeRemind(userUpdateRequest.getHoroscopeRemind());
            }
            if(Objects.nonNull(userUpdateRequest.getRemindTime())){
                if (!Objects.equals(userInfo.getRemindTime(), userUpdateRequest.getRemindTime())) {
                    horoscopeChanged = true;
                }
                userInfo.setRemindTime(userUpdateRequest.getRemindTime());
            }
            if(Objects.nonNull(userUpdateRequest.getRetrogradeWarning())){
                if (!Objects.equals(userInfo.getRetrogradeWarning(), userUpdateRequest.getRetrogradeWarning())) {
                    retrogradeWarningChanged = true;
                }
                userInfo.setRetrogradeWarning(userUpdateRequest.getRetrogradeWarning());
            }
            if(Objects.nonNull(userUpdateRequest.getPlanetChange())){
                if (!Objects.equals(userInfo.getPlanetChange(), userUpdateRequest.getPlanetChange())) {
                    planetChangeChanged = true;
                }
                userInfo.setPlanetChange(userUpdateRequest.getPlanetChange());
            }
            if(Objects.nonNull(userUpdateRequest.getMoonPhase())){
                if (!Objects.equals(userInfo.getMoonPhase(), userUpdateRequest.getMoonPhase())) {
                    moonPhaseChanged = true;
                }
                userInfo.setMoonPhase(userUpdateRequest.getMoonPhase());
            }
            if(Objects.nonNull(userUpdateRequest.getEclipse())){
                if (!Objects.equals(userInfo.getEclipse(), userUpdateRequest.getEclipse())) {
                    eclipseChanged = true;
                }
                userInfo.setEclipse(userUpdateRequest.getEclipse());
            }
            if(Objects.nonNull(userUpdateRequest.getMoonVoid())){
                if (!Objects.equals(userInfo.getMoonVoid(), userUpdateRequest.getMoonVoid())) {
                    moonVoidChanged = true;
                }
                userInfo.setMoonVoid(userUpdateRequest.getMoonVoid());
            }
            if(Objects.nonNull(userUpdateRequest.getPlanetAspect())){
                if (!Objects.equals(userInfo.getPlanetAspect(), userUpdateRequest.getPlanetAspect())) {
                    planetAspectChanged = true;
                }
                userInfo.setPlanetAspect(userUpdateRequest.getPlanetAspect());
            }
            
            userInfoMapper.saveAndFlush(userInfo);
            
            // 如果用户已注册推送ID，则处理各类推送设置的更新
            if (Objects.nonNull(userInfo.getRegistrationId())) {
                // 处理星座类型变化
                if (zodiacSignTypeChanged && oldZodiacSignType != null) {
                    // 如果星座类型变化且星座提醒已开启，需要更新Redis数据
                    if (userInfo.getHoroscopeRemind() == 1 && userInfo.getRemindTime() != null) {
                        // 从旧星座类型的Redis键中移除用户ID
                        updateZodiacPushSettings(userId, oldZodiacSignType, userInfo.getRemindTime(), ActionUnit.DELETE);
                        
                        // 添加到新星座类型的Redis键中
                        updateZodiacPushSettings(userId, userInfo.getZodiacSignType(), userInfo.getRemindTime(), ActionUnit.ADD);
                    }
                }
                
                // 处理星座提醒设置变化
                if (horoscopeChanged) {
                    // 从Redis移除旧设置
                    if (oldHoroscopeRemind == 1 && oldRemindTime != null && userInfo.getZodiacSignType() != null) {
                        updateZodiacPushSettings(userId, userInfo.getZodiacSignType(), oldRemindTime, ActionUnit.DELETE);
                    }
                    
                    // 添加新设置（如果启用）
                    if (userInfo.getHoroscopeRemind() == 1 && userInfo.getRemindTime() != null && userInfo.getZodiacSignType() != null) {
                        updateZodiacPushSettings(userId, userInfo.getZodiacSignType(), userInfo.getRemindTime(), ActionUnit.ADD);
                    }
                }
                
                // 处理逆行警告设置变化
                if (retrogradeWarningChanged) {
                    handlePushSettingChange(userId, "retrograde:warning", oldRetrogradeWarning, userInfo.getRetrogradeWarning());
                }
                
                // 处理行星变化设置变化
                if (planetChangeChanged) {
                    handlePushSettingChange(userId, "planet:change", oldPlanetChange, userInfo.getPlanetChange());
                }
                
                // 处理月相设置变化
                if (moonPhaseChanged) {
                    handlePushSettingChange(userId, "moon:phase", oldMoonPhase, userInfo.getMoonPhase());
                }
                
                // 处理日食月食设置变化
                if (eclipseChanged) {
                    handlePushSettingChange(userId, "eclipse", oldEclipse, userInfo.getEclipse());
                }
                
                // 处理空亡设置变化
                if (moonVoidChanged) {
                    handlePushSettingChange(userId, "moon:void", oldMoonVoid, userInfo.getMoonVoid());
                }
                
                // 处理行星相位设置变化
                if (planetAspectChanged) {
                    handlePushSettingChange(userId, "planet:aspect", oldPlanetAspect, userInfo.getPlanetAspect());
                }
            }
            
            return 1;
        }
        return 0;
    }

    /**
     * 处理推送设置的变更
     * @param userId 用户ID
     * @param settingType 设置类型
     * @param oldValue 旧值
     * @param newValue 新值
     */
    private void handlePushSettingChange(Long userId, String settingType, Integer oldValue, Integer newValue) {
        // 从Redis移除旧设置
        if (oldValue == 1) {
            updatePushSettings(userId, settingType, null, ActionUnit.DELETE);
        }
        
        // 添加新设置（如果启用）
        if (newValue == 1) {
            updatePushSettings(userId, settingType, null, ActionUnit.ADD);
        }
    }

    /**
     * 更新用户的推送设置到Redis
     * @param userId 用户ID
     * @param settingType 设置类型，如 "horoscope:remind", "retrograde:warning" 等
     * @param timeValue 时间值，仅星座提醒等需要具体时间的设置使用
     * @param actionUnit 操作类型（添加或删除）
     */
    @Override
    public void updatePushSettings(Long userId, String settingType, String timeValue, ActionUnit actionUnit) {
        // 对于星座提醒需要特殊处理，按星座类型分开存储
        if ("horoscope:remind".equals(settingType)) {
            // 获取用户信息，包括星座类型
            Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
            if (!userInfoOptional.isPresent()) {
                return;
            }
            
            UserInfo userInfo = userInfoOptional.get();
            ZodiacSignType zodiacSignType = userInfo.getZodiacSignType();
            
            // 如果用户没有设置星座，则不进行推送设置
            if (zodiacSignType == null) {
                return;
            }
            
            // 使用专门的方法处理星座推送设置
            updateZodiacPushSettings(userId, zodiacSignType, timeValue, actionUnit);
        }
        // 其他类型的推送设置
        else {
            RLock rLock = redissonClient.getFairLock("push:settings:" + settingType);
            rLock.lock();
            try {
                String key = "user:push:" + settingType;
                String field = timeValue != null ? timeValue : "all";
                int userIdInt = Math.toIntExact(userId);
                
                String existingIdsStr = redisService.hget(key, field);
                JSONArray existingIds = existingIdsStr == null ? new JSONArray() : JSONArray.parseArray(existingIdsStr);
                
                if (ActionUnit.ADD.equals(actionUnit)) {
                    // 检查是否已存在该用户ID
                    boolean userExists = false;
                    for (int i = 0; i < existingIds.size(); i++) {
                        if (existingIds.getInteger(i) == userIdInt) {
                            userExists = true;
                            break;
                        }
                    }
                    
                    // 如果用户ID不存在，则添加
                    if (!userExists) {
                        existingIds.add(userIdInt);
                        redisService.hset(key, field, existingIds.toJSONString());
                    }
                } 
                else if (ActionUnit.DELETE.equals(actionUnit)) {
                    // 删除用户ID
                    for (int i = 0; i < existingIds.size(); i++) {
                        if (existingIds.getInteger(i) == userIdInt) {
                            existingIds.remove(i);
                            break;
                        }
                    }
                    
                    // 如果列表为空，删除整个时间项，否则更新列表
                    if (existingIds.isEmpty()) {
                        redisService.hdel(key, field);
                    } else {
                        redisService.hset(key, field, existingIds.toJSONString());
                    }
                }
            } finally {
                rLock.unlock();
            }
        }
    }
    
    /**
     * 更新总的推送列表，用于兼容旧版本
     */
    private void updateGeneralPushList(Long userId, String settingType, String timeValue, ActionUnit actionUnit, Integer zodiacIndex) {
        String key = "user:push:" + settingType;
        String field = timeValue != null ? timeValue : "all";
        int userIdInt = Math.toIntExact(userId);
        
        String existingIdsStr = redisService.hget(key, field);
        JSONArray existingIds = existingIdsStr == null ? new JSONArray() : JSONArray.parseArray(existingIdsStr);
        
        if (ActionUnit.ADD.equals(actionUnit)) {
            // 检查是否已存在该用户ID
            boolean userExists = false;
            for (int i = 0; i < existingIds.size(); i++) {
                JSONObject userObj = existingIds.getJSONObject(i);
                if (userObj != null && userObj.getInteger("id") == userIdInt) {
                    userExists = true;
                    // 更新星座类型
                    userObj.put("zodiacSignType", zodiacIndex);
                    break;
                }
            }
            
            // 如果用户ID不存在，则添加
            if (!userExists) {
                JSONObject userObj = new JSONObject();
                userObj.put("id", userIdInt);
                userObj.put("zodiacSignType", zodiacIndex);
                existingIds.add(userObj);
            }
            
            redisService.hset(key, field, existingIds.toJSONString());
        } 
        else if (ActionUnit.DELETE.equals(actionUnit)) {
            // 删除用户ID
            for (int i = 0; i < existingIds.size(); i++) {
                JSONObject userObj = existingIds.getJSONObject(i);
                if (userObj != null && userObj.getInteger("id") == userIdInt) {
                    existingIds.remove(i);
                    break;
                }
            }
            
            // 如果列表为空，删除整个时间项，否则更新列表
            if (existingIds.isEmpty()) {
                redisService.hdel(key, field);
            } else {
                redisService.hset(key, field, existingIds.toJSONString());
            }
        }
    }

    public void registerPush(UserInfo userInfo,String registrationId){
        if(Objects.isNull(userInfo.getRegistrationId())){
            jushService.registerPush(registrationId,String.valueOf(userInfo.getUserId()));
            userInfo.setRegistrationId(registrationId);
            
            // 初始化事件通知
            List<ProfilePrompt> profilePrompts = profilePromptMapper.findByUserId(userInfo.getUserId());
            // 初始化推送数据
            initPushData(profilePrompts, ActionUnit.ADD, null);
            
            // 初始化所有推送设置
            initUserPushSettings(userInfo);
        }else{
            if(!userInfo.getRegistrationId().contains(registrationId)){
                jushService.registerPush(registrationId,String.valueOf(userInfo.getUserId()));
                userInfo.setRegistrationId(userInfo.getRegistrationId()+","+registrationId);
            }
        }
    }
    
    /**
     * 初始化用户的所有推送设置
     * @param userInfo 用户信息
     */
    private void initUserPushSettings(UserInfo userInfo) {
        // 初始化星座提醒推送设置
        if (userInfo.getHoroscopeRemind() == 1 && Objects.nonNull(userInfo.getRemindTime())) {
            updatePushSettings(userInfo.getUserId(), "horoscope:remind", userInfo.getRemindTime(), ActionUnit.ADD);
        }
        
        // 初始化逆行警告推送设置
        if (userInfo.getRetrogradeWarning() == 1) {
            updatePushSettings(userInfo.getUserId(), "retrograde:warning", null, ActionUnit.ADD);
        }
        
        // 初始化行星变化推送设置
        if (userInfo.getPlanetChange() == 1) {
            updatePushSettings(userInfo.getUserId(), "planet:change", null, ActionUnit.ADD);
        }
        
        // 初始化月相推送设置
        if (userInfo.getMoonPhase() == 1) {
            updatePushSettings(userInfo.getUserId(), "moon:phase", null, ActionUnit.ADD);
        }
        
        // 初始化日食月食推送设置
        if (userInfo.getEclipse() == 1) {
            updatePushSettings(userInfo.getUserId(), "eclipse", null, ActionUnit.ADD);
        }
        
        // 初始化空亡推送设置
        if (userInfo.getMoonVoid() == 1) {
            updatePushSettings(userInfo.getUserId(), "moon:void", null, ActionUnit.ADD);
        }
        
        // 初始化行星相位推送设置
        if (userInfo.getPlanetAspect() == 1) {
            updatePushSettings(userInfo.getUserId(), "planet:aspect", null, ActionUnit.ADD);
        }
    }

    @Override
    public void initPushData(List<ProfilePrompt> profilePrompts, ActionUnit actionUnit,Integer day){
        RLock rLock = redissonClient.getFairLock("userpush:init");
        rLock.lock();
        try {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("hh:mm");
            for (ProfilePrompt profilePrompt : profilePrompts) {
                int userId = Math.toIntExact(profilePrompt.getUserId());
                String promptTime = profilePrompt.getPromptTime().format(timeFormatter);

                List<Map<String,String>> timeList = new ArrayList<>();

                LocalDate now = LocalDate.now();
                if(Objects.nonNull(day)){
                    now = now.minusDays(day);
                }
                Map<String, String> solarBirMap = calculateNextBirthday(profilePrompt.getSolarTime().toLocalDate(),now);
                String solarNextBirthday = solarBirMap.get("nextBirthday");

                String[] lunarSpl = profilePrompt.getLunarTime().split(" ")[0].split("/");
                Map<String, String> lunarBirMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),now);
                String lunarNextBirthDay =lunarBirMap.get("nextBirthday");
                lunarSpl = lunarNextBirthDay.split("/");
                // 双历提醒
                if(profilePrompt.getPromptFlag().equals(DefaultFlag.YES)){
                    Map<String,String> solarMap = new HashMap<>();
                    solarMap.put("time",LocalDate.parse(solarNextBirthday, dateFormatter).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("oneDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(1).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("threeDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(3).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("sevenDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(7).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("fifteenDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(15).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("thirtyDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(30).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("birthDayType",BirthdayType.SOLAR.toString());
                    timeList.add(solarMap);
                    Map<String,String> lunarMap = new HashMap<>();
                    LunarDate lunarDate = LunarDate.of(Integer.parseInt(lunarSpl[0]), Integer.parseInt(lunarSpl[1]), Integer.parseInt(lunarSpl[2]));
                    lunarMap.put("time", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(1, ChronoUnit.DAYS));
                    lunarMap.put("oneDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(3, ChronoUnit.DAYS));
                    lunarMap.put("threeDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(7, ChronoUnit.DAYS));
                    lunarMap.put("sevenDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(15, ChronoUnit.DAYS));;
                    lunarMap.put("fifteenDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(30, ChronoUnit.DAYS));
                    lunarMap.put("thirtyDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarMap.put("birthDayType",BirthdayType.LUNAR.toString());
                    timeList.add(lunarMap);
                }
                else if(profilePrompt.getBirthdayType().equals(BirthdayType.SOLAR)){
                    Map<String,String> solarMap = new HashMap<>();
                    solarMap.put("time",LocalDate.parse(solarNextBirthday, dateFormatter).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("oneDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(1).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("threeDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(3).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("sevenDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(7).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("fifteenDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(15).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("thirtyDayTime",LocalDate.parse(solarNextBirthday, dateFormatter).minusDays(30).format(dateFormatter) + ":" + promptTime);
                    solarMap.put("birthDayType",BirthdayType.SOLAR.toString());
                    timeList.add(solarMap);
                }
                else if(profilePrompt.getBirthdayType().equals(BirthdayType.LUNAR)){
                    Map<String,String> lunarMap = new HashMap<>();
                    LunarDate lunarDate = LunarDate.of(Integer.parseInt(lunarSpl[0]), Integer.parseInt(lunarSpl[1]), Integer.parseInt(lunarSpl[2]));
                    lunarMap.put("time", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(1, ChronoUnit.DAYS));
                    lunarMap.put("oneDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(3, ChronoUnit.DAYS));
                    lunarMap.put("threeDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(7, ChronoUnit.DAYS));
                    lunarMap.put("sevenDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(15, ChronoUnit.DAYS));;
                    lunarMap.put("fifteenDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarDate = LunarDate.from(lunarDate.minus(30, ChronoUnit.DAYS));
                    lunarMap.put("thirtyDayTime", lunarDate.getlYearCn()+"-"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"-"+lunarDate.getlDayCn()+":"+promptTime);
                    lunarMap.put("birthDayType",BirthdayType.LUNAR.toString());
                    timeList.add(lunarMap);
                }
                for (Map<String, String> infoMap : timeList) {
                    for (Map.Entry<String, String> entry : infoMap.entrySet()) {
                        if(entry.getValue().length()==1){
                            return;
                        }
                        Boolean flag = false;
                        String time = entry.getValue();
                        String key = null;
                        if(entry.getKey().equals("time") && profilePrompt.getOnTheDay().equals(DefaultFlag.YES)){
                            flag = true;
                            key = infoMap.get("birthDayType").equals(BirthdayType.SOLAR.toString()) ? RedisKeyConstant.USER_PUSH_SOLAR_DAILY_PROMPT : RedisKeyConstant.USER_PUSH_LUNAR_DAILY_PROMPT;
                        }else if(entry.getKey().equals("oneDayTime") && profilePrompt.getOneDayBefore().equals(DefaultFlag.YES)){
                            flag = true;
                            key = infoMap.get("birthDayType").equals(BirthdayType.SOLAR.toString()) ? RedisKeyConstant.USER_PUSH_SOLAR_ONE_DAY_PROMPT : RedisKeyConstant.USER_PUSH_LUNAR_ONE_DAY_PROMPT;
                        }else if(entry.getKey().equals("threeDayTime") && profilePrompt.getThreeDaysBefore().equals(DefaultFlag.YES)){
                            flag = true;
                            key = infoMap.get("birthDayType").equals(BirthdayType.SOLAR.toString()) ? RedisKeyConstant.USER_PUSH_SOLAR_THREE_DAY_PROMPT : RedisKeyConstant.USER_PUSH_LUNAR_THREE_DAY_PROMPT;
                        }else if(entry.getKey().equals("sevenDayTime") && profilePrompt.getSevenDaysBefore().equals(DefaultFlag.YES)){
                            flag = true;
                            key = infoMap.get("birthDayType").equals(BirthdayType.SOLAR.toString()) ? RedisKeyConstant.USER_PUSH_SOLAR_SEVEN_DAY_PROMPT : RedisKeyConstant.USER_PUSH_LUNAR_SEVEN_DAY_PROMPT;
                        }else if(entry.getKey().equals("fifteenDayTime") && profilePrompt.getFifteenDaysBefore().equals(DefaultFlag.YES)){
                            flag = true;
                            key = infoMap.get("birthDayType").equals(BirthdayType.SOLAR.toString()) ? RedisKeyConstant.USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT : RedisKeyConstant.USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT;
                        }else if(entry.getKey().equals("thirtyDayTime") && profilePrompt.getThirtyDaysBefore().equals(DefaultFlag.YES)){
                            flag = true;
                            key = infoMap.get("birthDayType").equals(BirthdayType.SOLAR.toString()) ? RedisKeyConstant.USER_PUSH_SOLAR_THIRTY_DAY_PROMPT : RedisKeyConstant.USER_PUSH_LUNAR_THIRTY_DAY_PROMPT;
                        }
                        if(flag){
                            String existingIdsStr = redisService.hget(key,time);
                            JSONArray existingIds = existingIdsStr == null ? new JSONArray() : JSONArray.parseArray(existingIdsStr);
                            if(ActionUnit.ADD.equals(actionUnit)){
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("msg",profilePrompt.getName());
                                // 检查是否已有相同 msg
                                boolean msgExists = false;
                                for (int i = 0; i < existingIds.size(); i++) {
                                    JSONObject existingObject = existingIds.getJSONObject(i);
                                    if (existingObject.getString("msg").equals(profilePrompt.getName())) {
                                        msgExists = true;
                                        // 如果 msg 相同，将 id 添加到数组中
                                        if (existingObject.containsKey("id")) {
                                            Object idValue = existingObject.get("id");
                                            JSONArray idArray;
                                            if (idValue instanceof JSONArray) {
                                                idArray = (JSONArray) idValue;
                                            } else {
                                                idArray = new JSONArray();
                                                idArray.add(idValue);
                                            }
                                            idArray.add(userId);
                                            existingObject.put("id", idArray);
                                        } else {
                                            JSONArray idArray = new JSONArray();
                                            idArray.add(existingObject.getString("id"));
                                            idArray.add(userId);
                                            existingObject.put("id", idArray);
                                        }
                                        break;
                                    }
                                }

                                // 如果 msg 不存在，直接添加新对象
                                if (!msgExists) {
                                    jsonObject.put("id", Arrays.asList(userId));
                                    existingIds.add(jsonObject);
                                }
                                redisService.hset(key,time,existingIds.toJSONString());
                            }
                            else if(ActionUnit.DELETE.equals(actionUnit)){
                                for (int i = 0; i < existingIds.size(); i++) {
                                    JSONObject existingObject = existingIds.getJSONObject(i);
                                    if (existingObject.getString("msg").equals(profilePrompt.getName())) {
                                        Object idValue = existingObject.get("id");
                                        JSONArray idArray;
                                        if (idValue instanceof JSONArray) {
                                            idArray = (JSONArray) idValue;
                                        } else {
                                            idArray = new JSONArray();
                                            idArray.add(idValue);
                                        }
                                        idArray.removeIf(id -> id.equals(userId));
                                        existingObject.put("id", idArray);

                                        if(idArray.size()==0){
                                            redisService.hdel(key,time);
                                        }else{
                                            redisService.hset(key,time,existingIds.toJSONString());
                                        }

                                    }
                                }
                            }
                        }
                    }
                }
            }
        } finally {
            rLock.unlock();
        }
    }

    @Override
    public int updateVipExpireDate(Long userId, LocalDateTime vipExpireDate, String productId,Integer vip) {
        return userInfoMapper.updateVipExpiryDateByUserId(userId, vipExpireDate, productId, vip);
    }

    @Override
    public int updateFreeUse(Long userId) {
        return userInfoMapper.updateFreeUse(userId);
    }

    @Override
    public int updateRewardVipBeginDate(Long userId, LocalDateTime rewardVipBeginDate) {
        return userInfoMapper.updateRewardVipBeginDate(userId, rewardVipBeginDate);
    }

    /**
     * 用户登录函数，通过手机号登录
     *
     * @param userLoginRequest 手机号
     * @return 登录用户对象
     */
    @Override
    public UserInfoVO login(UserLoginRequest userLoginRequest,UserInfoVO beforeUserInfoVO) {
        // 通过手机号查询用户
        UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
        userInfoBaseRequest.setPhone(userLoginRequest.getPhone());
        userInfoBaseRequest.setState(USER_NORMAL);
        Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
        UserInfo userInfo;
        // 如果用户不存在，即为绑定账号
        if(!userInfoOptional.isPresent()){
            String invitationCode = generateInvitationCode();
            while (Objects.nonNull(userInfoMapper.findByInvitationCode(invitationCode))){
                invitationCode = generateInvitationCode();
            }
            userInfo = userInfoMapper.findById(beforeUserInfoVO.getUserId()).get();
            if(Objects.nonNull(userLoginRequest.getRegistrationId())){
                jushService.registerPush(userLoginRequest.getRegistrationId(),String.valueOf(userInfo.getUserId()));
                if(Objects.isNull(userInfo.getRegistrationId())){
                    userInfo.setRegistrationId(userLoginRequest.getRegistrationId());
                }else{
                    userInfo.setRegistrationId(userInfo.getRegistrationId()+","+userLoginRequest.getRegistrationId());
                }
            }
            userInfo.setPhone(userLoginRequest.getPhone());
            userInfo.setTouristState(DefaultFlag.NO);
            userInfoMapper.save(userInfo);
        }
        // 换绑账号
        else {
            userInfo = userInfoOptional.get();
            // 更新uuid
            userInfo.setUuid(beforeUserInfoVO.getUuid());
            // 更新注册别名
            String regIds;
            if(Objects.isNull(beforeUserInfoVO.getRegistrationId())){
                regIds = userInfo.getRegistrationId();
            }else{
                regIds = userInfo.getRegistrationId() + "," + beforeUserInfoVO.getRegistrationId();
            }
            if(Objects.nonNull(regIds)){
                Set<String> newIdSet = new HashSet<>(Arrays.asList(regIds.split(",")));
                StringBuilder uniqueIds = new StringBuilder();
                for (String id : newIdSet) {
                    if (uniqueIds.length() > 0) {
                        uniqueIds.append(",");
                    }
                    uniqueIds.append(id);
                }
                userInfo.setRegistrationId(uniqueIds.toString());
            }
            if(Objects.nonNull(userInfo.getRegistrationId())){
                jushService.registerPush(beforeUserInfoVO.getRegistrationId(),String.valueOf(userInfo.getUserId()));
            }
            userInfoMapper.save(userInfo);
            if(!beforeUserInfoVO.getUserId().equals(userInfo.getUserId())){
                // 原账号注销
                cancelUser(beforeUserInfoVO.getUserId());
            }
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(userInfo ,userInfoVO);
        return userInfoVO;
    }

    /**
     * 苹果登录
     * @param userLoginWithAppleRequest
     * @param beforeUserInfoVO
     * @return
     */
    @Override
    public UserInfoVO loginWithApple(UserLoginWithAppleRequest userLoginWithAppleRequest, UserInfoVO beforeUserInfoVO) {
        // 获取用户apple唯一凭证
        JSONObject jsonObject = IOSToeknUtils.parserIdentityToken(userLoginWithAppleRequest.getIdentityToken());
        String appleId = jsonObject.getString("sub");
        // 通过手机号查询用户
        UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
        userInfoBaseRequest.setAppleId(appleId);
        userInfoBaseRequest.setState(USER_NORMAL);
        Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
        UserInfo userInfo;
        // 如果用户不存在，即为绑定账号
        if(!userInfoOptional.isPresent()){
            String invitationCode = generateInvitationCode();
            while (Objects.nonNull(userInfoMapper.findByInvitationCode(invitationCode))){
                invitationCode = generateInvitationCode();
            }
            userInfo = userInfoMapper.findById(beforeUserInfoVO.getUserId()).get();
            if(Objects.nonNull(userLoginWithAppleRequest.getRegistrationId())){
                jushService.registerPush(userLoginWithAppleRequest.getRegistrationId(),String.valueOf(userInfo.getUserId()));
                if(Objects.isNull(userInfo.getRegistrationId())){
                    userInfo.setRegistrationId(userLoginWithAppleRequest.getRegistrationId());
                }else{
                    userInfo.setRegistrationId(userInfo.getRegistrationId()+","+userLoginWithAppleRequest.getRegistrationId());
                }
            }
            userInfo.setAppleId(appleId);
            userInfo.setTouristState(DefaultFlag.NO);
            userInfoMapper.save(userInfo);
        }
        // 换绑账号
        else {
            userInfo = userInfoOptional.get();
            // 更新uuid
            userInfo.setUuid(beforeUserInfoVO.getUuid());
            // 更新注册别名
            String regIds;
            if(Objects.isNull(beforeUserInfoVO.getRegistrationId())){
                regIds = userInfo.getRegistrationId();
            }else{
                regIds = userInfo.getRegistrationId() + "," + beforeUserInfoVO.getRegistrationId();
            }
            if(Objects.nonNull(regIds)){
                Set<String> newIdSet = new HashSet<>(Arrays.asList(regIds.split(",")));
                StringBuilder uniqueIds = new StringBuilder();
                for (String id : newIdSet) {
                    if (uniqueIds.length() > 0) {
                        uniqueIds.append(",");
                    }
                    uniqueIds.append(id);
                }
                userInfo.setRegistrationId(uniqueIds.toString());
            }
            if(Objects.nonNull(userInfo.getRegistrationId())){
                jushService.registerPush(beforeUserInfoVO.getRegistrationId(),String.valueOf(userInfo.getUserId()));
            }
            userInfoMapper.save(userInfo);
            if(!beforeUserInfoVO.getUserId().equals(userInfo.getUserId())){
                // 原账号注销
                cancelUser(beforeUserInfoVO.getUserId());
            }
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(userInfo ,userInfoVO);
        return userInfoVO;
    }

    /**
     * 游客登录函数，通过UUID登录
     *
     * @param touristsLoginRequest TouristsLoginRequest
     * @return 登录用户对象
     */
    @Override
    public UserInfoVO touristsLogin(TouristsLoginRequest touristsLoginRequest) {
        // 通过UUID查询用户
        UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
        userInfoBaseRequest.setUuid(touristsLoginRequest.getUuid());
        userInfoBaseRequest.setState(USER_NORMAL);
        Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
        UserInfo userInfo;
        // 如果用户不存在，即为未注册
        if(!userInfoOptional.isPresent()){
            String invitationCode = generateInvitationCode();
            while (Objects.nonNull(userInfoMapper.findByInvitationCode(invitationCode))){
                invitationCode = generateInvitationCode();
            }
            userInfo = UserInfo.builder()
                    .uuid(touristsLoginRequest.getUuid())
                    .nickname(generateUserName())
                    .vip(NOT_VIP)
                    .avatar(domain+DEFAULT_AVATAR)
                    .createDate(LocalDateTime.now())
                    .modifyDate(LocalDateTime.now())
                    .state(USER_NORMAL)
                    .invitationCode(invitationCode)
                    .vipExpiryDate(null)
                    .vipHours(0)
                    .totalVipHours(0)
                    .point(0)
                    .totalPoint(0)
                    .star5State(DefaultFlag.NO)
                    .freeUsed(DefaultFlag.NO)
                    .touristState(DefaultFlag.YES)
                    .osType(touristsLoginRequest.getOsType())
                    .registrationId(touristsLoginRequest.getRegistrationId())
                    .horoscopeRemind(1)
                    .remindTime("08:00")
                    .retrogradeWarning(1)
                    .planetChange(1)
                    .moonPhase(1)
                    .eclipse(0)
                    .moonVoid(0)
                    .planetAspect(0)
                    .build();
            userInfoMapper.saveAndFlush(userInfo);
            if(Objects.nonNull(touristsLoginRequest.getRegistrationId())){
                jushService.registerPush(touristsLoginRequest.getRegistrationId(),String.valueOf(userInfo.getUserId()));
            }
        } else {
            userInfo = userInfoOptional.get();
            if(Objects.nonNull(touristsLoginRequest.getRegistrationId()) && !userInfo.getRegistrationId().equals(touristsLoginRequest.getRegistrationId())){
                jushService.registerPush(touristsLoginRequest.getRegistrationId(),String.valueOf(userInfo.getUserId()));
                userInfo.setRegistrationId(touristsLoginRequest.getRegistrationId());
                userInfoMapper.saveAndFlush(userInfo);
            }
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(userInfo ,userInfoVO);
        return userInfoVO;
    }

    /**
     * 用户注销
     * @param userId
     * @return
     */
    @Override
    public boolean cancelUser(Long userId) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if(userInfoOptional.isPresent()){
            UserInfo userInfo = userInfoOptional.get();
            userInfo.setState(USER_CANCELED);
            userInfoMapper.saveAndFlush(userInfo);
        }else {
            return false;
        }
        return true;
    }

    @Override
    public CdKey exchange(String phone,Integer cdkType,LocalDate date) {
        UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
        userInfoBaseRequest.setPhone(phone);
        userInfoBaseRequest.setState(USER_NORMAL);
        Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
        UserInfo userInfo;
        if(!userInfoOptional.isPresent()){
            String invitationCode = generateInvitationCode();
            while (Objects.nonNull(userInfoMapper.findByInvitationCode(invitationCode))){
                invitationCode = generateInvitationCode();
            }
            userInfo = UserInfo.builder()
                    .uuid("")
                    .phone(phone)
                    .nickname(generateUserName())
                    .avatar(domain+DEFAULT_AVATAR)
                    .createDate(LocalDateTime.now())
                    .modifyDate(LocalDateTime.now())
                    .state(USER_NORMAL)
                    .invitationCode(invitationCode)
                    .vipExpiryDate(null)
                    .vipHours(0)
                    .totalVipHours(0)
                    .point(0)
                    .totalPoint(0)
                    .star5State(DefaultFlag.NO)
                    .freeUsed(DefaultFlag.NO)
                    .touristState(DefaultFlag.NO)
                    .build();
        }else{
            userInfo = userInfoOptional.get();
        }
        userInfo.setVip(IS_VIP);
        LocalDateTime now = LocalDateTime.now();
        if(Objects.nonNull(userInfo.getCdKeyExpiryDate()) && userInfo.getCdKeyExpiryDate().isAfter(now)){
            now = userInfo.getCdKeyExpiryDate();
        }
        LocalDateTime expiryDate = getExpiryDate(now, cdkType, date);
        userInfo.setCdKeyExpiryDate(expiryDate);
        userInfoMapper.saveAndFlush(userInfo);
        return CdKey.builder()
                .startDate(now)
                .useDate(LocalDateTime.now())
                .expiryDate(expiryDate)
                .build();
    }

    /**
     * 过期会员
     * @param phone
     * @return
     */
    @Override
    public Result expire(String phone) {
        UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
        userInfoBaseRequest.setPhone(phone);
        userInfoBaseRequest.setState(USER_NORMAL);
        Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
        if(userInfoOptional.isPresent()){
            UserInfo userInfo = userInfoOptional.get();
            userInfo.setVip(NOT_VIP);
            userInfo.setCdKeyExpiryDate(LocalDateTime.now());
            userInfoMapper.saveAndFlush(userInfo);
            return Result.ok();
        }else{
            return Result.error("未找到该用户");
        }
    }

    private static LocalDateTime getExpiryDate(LocalDateTime now, Integer type, LocalDate date){
        LocalDateTime expiryDate = now;
        if(Objects.nonNull(date)){
            expiryDate = LocalDateTime.of(date, LocalTime.MAX);
        }else if(Objects.nonNull(type)){
            if(OperateUnit.DAY.toValue()==type){
                expiryDate = now.plusDays(1);
            }else if(OperateUnit.WEEK.toValue()==type){
                expiryDate = now.plusWeeks(1);
            }else if(OperateUnit.MONTH.toValue()==type){
                expiryDate = now.plusMonths(1);
            }else if(OperateUnit.QUARTER.toValue()==type){
                expiryDate = now.plusMonths(3);
            }else if(OperateUnit.HALFYEAR.toValue()==type){
                expiryDate = now.plusMonths(6);
            }else if(OperateUnit.YEAR.toValue()==type){
                expiryDate = now.plusYears(1);
            }else if(OperateUnit.DECADE.toValue()==type){
                expiryDate = now.plusYears(10);
            }else if(OperateUnit.YEAR100.toValue()==type){
                expiryDate = now.plusYears(100);
            }
        }
        return expiryDate;
    }

    public static String generateUserName() {
        Random random = new Random();
        String randomNumber = String.format("%06d", random.nextInt(1000000)); // 生成6位随机数字
        return "用户" + randomNumber; // 拼接用户名和随机数字
    }

    public static String generateInvitationCode() {
        return RandomUtil.randomString("abcdefghijklmnopqrstuvwxyz", 6);
    }

    public void checkVipExpiryDate(UserInfoVO userInfo) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime iapExpiryDate = userInfo.getVipExpiryDate();
        LocalDateTime rewardVipBeginDate = userInfo.getRewardVipBeginDate();
        if (iapExpiryDate != null && rewardVipBeginDate != null) {
            LocalDateTime expiryDateToSet = iapExpiryDate.isAfter(rewardVipBeginDate) ? iapExpiryDate : rewardVipBeginDate;

            userInfo.setVipExpiryDate(expiryDateToSet);
            if (expiryDateToSet.isAfter(now)) {
                userInfo.setVip(1); // VIP状态为1表示有效
            }
        } else if (iapExpiryDate != null) {
            userInfo.setVipExpiryDate(iapExpiryDate);
            if (iapExpiryDate.isAfter(now)) {
                userInfo.setVip(1); // VIP状态为1表示有效
            }
        } else if (rewardVipBeginDate != null) {
            userInfo.setVipExpiryDate(rewardVipBeginDate);
            if (rewardVipBeginDate.isAfter(now)) {
                userInfo.setVip(1); // VIP状态为1表示有效
            }
        }
    }

    private Specification<UserInfo> build(UserInfoBaseRequest userInfoBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // uuid
            if(userInfoBaseRequest.getUuid()!=null) {
                predicates.add(cbuild.equal(root.get("uuid"), userInfoBaseRequest.getUuid()));
            }
            // 手机号搜索
            if(userInfoBaseRequest.getPhone()!=null) {
                Predicate uuid = cbuild.equal(root.get("uuid"), userInfoBaseRequest.getUuid());
                Predicate phone = cbuild.equal(root.get("phone"), userInfoBaseRequest.getPhone());
                Predicate resultEndTime = cbuild.or(phone, uuid);
                predicates.add(resultEndTime);
            }
            // 苹果id搜索
            if(userInfoBaseRequest.getAppleId()!=null) {
                Predicate uuid = cbuild.equal(root.get("uuid"), userInfoBaseRequest.getUuid());
                Predicate appleId = cbuild.equal(root.get("appleId"), userInfoBaseRequest.getAppleId());
                Predicate resultEndTime = cbuild.or(appleId, uuid);
                predicates.add(resultEndTime);
            }
            // 用户注销状态
            if(userInfoBaseRequest.getState()!=null) {
                predicates.add(cbuild.equal(root.get("state"), userInfoBaseRequest.getState()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

    @Override
    public void updateRecoverDate(Long userId, LocalDate recoverDate) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if (userInfoOptional.isPresent()) {
            UserInfo userInfo = userInfoOptional.get();
            userInfo.setRecoverState(DefaultFlag.YES.toValue());
            userInfo.setRecoverDate(recoverDate);
            userInfoMapper.save(userInfo);
        }
    }

    @Override
    public void updateRecoverState(Long userId, Integer recoverState) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if (userInfoOptional.isPresent()) {
            UserInfo userInfo = userInfoOptional.get();
            userInfo.setRecoverState(recoverState);
            // 如果状态为0（关闭），则更新日期为当前日期
            if (recoverState == 0) {
                userInfo.setRecoverDate(LocalDate.now());
            }
            userInfoMapper.save(userInfo);
        }
    }

    /**
     * 更新用户的星座推送设置到Redis
     * @param userId 用户ID
     * @param zodiacSignType 星座类型
     * @param timeValue 提醒时间
     * @param actionUnit 操作类型（添加或删除）
     */
    @Override
    public void updateZodiacPushSettings(Long userId, ZodiacSignType zodiacSignType, String timeValue, ActionUnit actionUnit) {
        if (zodiacSignType == null) {
            return;
        }
        
        int zodiacIndex = zodiacSignType.toValue();
        String key = "user:push:horoscope:remind:" + zodiacIndex;
        String field = timeValue != null ? timeValue : "all";
        int userIdInt = Math.toIntExact(userId);
        
        RLock rLock = redissonClient.getFairLock("push:settings:horoscope:remind:" + zodiacIndex);
        rLock.lock();
        try {
            String existingIdsStr = redisService.hget(key, field);
            JSONArray existingIds = existingIdsStr == null ? new JSONArray() : JSONArray.parseArray(existingIdsStr);
            
            if (ActionUnit.ADD.equals(actionUnit)) {
                // 检查是否已存在该用户ID
                boolean userExists = false;
                for (int i = 0; i < existingIds.size(); i++) {
                    if (existingIds.getInteger(i) == userIdInt) {
                        userExists = true;
                        break;
                    }
                }
                
                // 如果用户ID不存在，则添加
                if (!userExists) {
                    existingIds.add(userIdInt);
                    redisService.hset(key, field, existingIds.toJSONString());
                }
            } 
            else if (ActionUnit.DELETE.equals(actionUnit)) {
                // 删除用户ID
                for (int i = 0; i < existingIds.size(); i++) {
                    if (existingIds.getInteger(i) == userIdInt) {
                        existingIds.remove(i);
                        break;
                    }
                }
                
                // 如果列表为空，删除整个时间项，否则更新列表
                if (existingIds.isEmpty()) {
                    redisService.hdel(key, field);
                } else {
                    redisService.hset(key, field, existingIds.toJSONString());
                }
            }
            
            // 同时更新总的推送列表，用于兼容
            updateGeneralPushList(userId, "horoscope:remind", timeValue, actionUnit, zodiacIndex);
        } finally {
            rLock.unlock();
        }
    }
}
