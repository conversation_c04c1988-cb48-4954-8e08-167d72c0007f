package com.lhx.birthday.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.entity.AnswerBook;
import com.lhx.birthday.enums.ZodiacSigns;
import com.lhx.birthday.mapper.AnswerBookMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.mapper.ZodiacSignsMapper;
import com.lhx.birthday.request.horoscope.ShierYunshiListRequest;
import com.lhx.birthday.request.horoscope.ShierYunshiRequest;
import com.lhx.birthday.service.IZodiacSignsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
@Slf4j
public class ZodiacSignsServiceImpl implements IZodiacSignsService {

    @Resource
    private ZodiacSignsMapper zodiacSignsMapper;

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Resource
    private AnswerBookMapper answerBookMapper;

    @Override
    public String getInfoByTimeAndType(ShierYunshiRequest shierYunshiRequest) {
        ZodiacSigns zodiacSigns = zodiacSignsMapper.findByTimeAndType(shierYunshiRequest.getTime(), shierYunshiRequest.getType());
        return zodiacSigns.getInfo();
    }

    @Override
    public JSONObject getAnswer() {
        JSONObject jsonObject = new JSONObject();
        AnswerBook rand = answerBookMapper.findRand();
        jsonObject.put("answer",rand.getAnswer());
        return jsonObject;
    }

    public static String getAstrologyOrZodiac(String dateStr, int type) {
        // 将字符串日期转换为 LocalDate 对象
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 获取年、月、日
        int year = date.getYear();
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();

        // 判断 type 类型
        if (type == 0) {
            return year + "年" + month + "月" + day + "日十二星座运势";
        } else if (type == 1) {
            return year + "年" + month + "月" + day + "日十二生肖运势";
        } else {
            return "type 参数不合法";
        }
    }
}
