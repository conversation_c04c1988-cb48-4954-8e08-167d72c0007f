package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.enums.ZodiacType;
import com.lhx.birthday.util.NumUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "user_info")
public class UserInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userId;

    private String openid;

    @Column(name = "uuid")
    private String uuid;

    @Column(name = "name")
    private String name;

    @Column(name = "nickname")
    private String nickname;

    @Column(name = "phone")
    private String phone;

    @Column(name = "apple_id")
    private String appleId;

    @Column(name = "avatar")
    private String avatar;

    @Column(name = "theme")
    private Integer theme;

    @Column(name = "device_token")
    private String deviceToken;

    @Column(name = "vip")
    private Integer vip;

    @Column(name = "vip_expiry_date")
    private LocalDateTime vipExpiryDate;

    @Column(name = "cdkey_expiry_date")
    @CreatedDate
    private LocalDateTime cdKeyExpiryDate;

    @Column(name = "vip_product_id")
    private String vipProductId;

    @Column(name = "state")
    private Integer state;

    @Column(name = "create_date")
    @CreatedDate
    private LocalDateTime createDate;

    @Column(name = "modify_date")
    @LastModifiedDate
    private LocalDateTime modifyDate;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    /**
     * 0未参与，1已参与
     */
    @Enumerated
    private DefaultFlag star5State;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 是否试用
     */
    @Enumerated
    private DefaultFlag freeUsed;

    /**
     * 积分
     */
    private Integer totalPoint;

    @Column(name = "point_t")
    private Integer point;

    /**
     * vip
     */
    private Integer vipHours;
    private Integer totalVipHours;

    private LocalDateTime rewardVipBeginDate;

    /**
     * 是否游客
     */
    @Enumerated
    private DefaultFlag touristState;

    private Long countryCodeId;

    private String osType;

    @Column(name = "registration_id")
    private String registrationId;

    private String channel;

    private String version;

    private String deviceBrand;

    private String ipAddr;

    /**
     * 生肖
     */
    @Enumerated
    @Column(name = "zodiac_type")
    private ZodiacType zodiacType;

    /**
     * 星座
     */
    @Enumerated
    @Column(name = "zodiac_sign_type")
    private ZodiacSignType zodiacSignType;

    @Column(name = "recover_state")
    @ApiModelProperty("挽回状态 0关闭 1开启")
    private Integer recoverState = 1;

    @Column(name = "recover_date")
    @ApiModelProperty("触发挽回的日期")
    private LocalDate recoverDate;

    @Column(name = "horoscope_remind")
    @ApiModelProperty("星座运势提醒 0关闭 1开启")
    private Integer horoscopeRemind = 1;
    
    @Column(name = "remind_time")
    @ApiModelProperty("提醒时间")
    private String remindTime = "08:00";

    // type 4
    @Column(name = "retrograde_warning")
    @ApiModelProperty("逆行预警 0关闭 1开启")
    private Integer retrogradeWarning = 1;

    // type 2
    @Column(name = "planet_change")
    @ApiModelProperty("行星换座 0关闭 1开启")
    private Integer planetChange = 1;

    // type 6 9
    @Column(name = "moon_phase")
    @ApiModelProperty("新月满月 0关闭 1开启")
    private Integer moonPhase = 1;

    // type 15 19 20 25
    @Column(name = "eclipse")
    @ApiModelProperty("日月食 0关闭 1开启")
    private Integer eclipse = 0;

    // type 13 14
    @Column(name = "moon_void")
    @ApiModelProperty("月亮空亡 0关闭 1开启")
    private Integer moonVoid = 0;

    // type 1
    @Column(name = "planet_aspect")
    @ApiModelProperty("行星相位 0关闭 1开启")
    private Integer planetAspect = 0;

    public void initRewardVip(UserInfo user, LocalDateTime aRewardVipBeginDate, Integer aVipHours) {
        if (NumUtil.lte(aVipHours, 0)) {
            clearRewardVip(user);
        } else {
            this.rewardVipBeginDate = aRewardVipBeginDate;
            this.vipHours = 0;
            user.setRewardVipBeginDate(aRewardVipBeginDate);
            user.setVipHours(0);
        }
    }

    public void clearRewardVip(UserInfo user) {
        this.rewardVipBeginDate = null;
        this.vipHours = 0;
        user.setRewardVipBeginDate(null);
        user.setVipHours(0);
    }


    public UserInfo() {

    }
}
