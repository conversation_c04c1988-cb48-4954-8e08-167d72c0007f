package com.lhx.birthday.vo;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.enums.ZodiacType;
import com.lhx.birthday.util.*;
import com.lhx.birthday.vo.systemConfig.DataSourceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Enumerated;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "用户信息响应体",description = "用户信息响应体")
public class UserInfoVO {

    @ApiModelProperty(name = "用户ID", value = "用户ID")
    private Long userId;

    @ApiModelProperty(name = "OpenID", value = "用户的OpenID")
    private String openid;

    @ApiModelProperty(name = "三方平台昵称", value = "用户在第三方平台的昵称")
    private String name;

    @ApiModelProperty(name = "uuid", value = "用户的UUID")
    private String uuid;

    @ApiModelProperty(name = "昵称", value = "用户的昵称")
    private String nickname;

    @ApiModelProperty(name = "手机号", value = "用户的手机号")
    private String phone;

    @ApiModelProperty(value = "苹果id", name = "appleId")
    private String appleId;

    @ApiModelProperty(name = "头像", value = "用户的头像URL")
    private String avatar;

    @ApiModelProperty(name = "主题颜色", value = "用户的主题颜色")
    private Integer theme;

    @ApiModelProperty(name = "推送设备", value = "用户的推送设备信息")
    private String deviceToken;

    @ApiModelProperty(name = "是否会员", value = "用户是否为会员，1表示是，0表示否")
    private Integer vip;

    @ApiModelProperty(name = "会员到期时间", value = "用户会员资格的到期时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime vipExpiryDate;

    @ApiModelProperty(name = "会员产品id", value = "用户的会员产品ID")
    private String vipProductId;

    @ApiModelProperty(name = "状态", value = "状态 0: 正常 1已注销")
    private Integer state;

    @ApiModelProperty(name = "真实姓名", value = "用户的真实姓名")
    private String realName;

    @ApiModelProperty(name = "出生年月", value = "用户的出生年月日")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate birthDate;

    @ApiModelProperty(name = "5星好评 0未参与，1已参与", value = "用户对产品的评价，0表示未参与，1表示已参与")
    private DefaultFlag star5State;

    private Integer vipHours;
    private Integer totalVipHours;

    private Integer totalPoint;
    private Integer point;

    private LocalDateTime rewardVipBeginDate;

    private LocalDateTime cdKeyExpiryDate;

    private String invitationCode;

    /**
     * 星座
     */
    @ApiModelProperty(name = "星座", value = "用户的星座")
    private ZodiacSignType zodiacSignType;

    @ApiModelProperty(name = "星座", value = "用户的星座")
    private String zodiacSignName;

    /**
     * 生肖
     */
    @ApiModelProperty(name = "生肖", value = "用户的生肖")
    private ZodiacType zodiacType;

    @ApiModelProperty(name = "生肖", value = "用户的生肖")
    private String zodiacName;

    @ApiModelProperty(value = "是否试用 0否 1是", name = "freeUsed")
    private DefaultFlag freeUsed;

    @ApiModelProperty(name = "注册天数", value = "注册天数")
    private Long regDay;

    @ApiModelProperty(value = "是否游客 0否 1是", name = "touristState")
    private DefaultFlag touristState;

    @ApiModelProperty(value = "是否拥有发布权限 0否 1是", name = "touristState")
    private DefaultFlag blogState;

    private Long countryCodeId;

    @ApiModelProperty(value = "老黄历查看权限 0否 1是", name = "touristState")
    private DefaultFlag almanacState;

    private String osType;

    private String registrationId;

    private String channel;
    private String version;
    private String deviceBrand;

    @ApiModelProperty("挽回状态 0关闭 1开启")
    private Integer recoverState;
    
    @ApiModelProperty("触发挽回的日期")
    private LocalDate recoverDate;

    @ApiModelProperty("星座运势提醒 0关闭 1开启")
    private Integer horoscopeRemind;
    
    @ApiModelProperty("提醒时间")
    private String remindTime;
    
    @ApiModelProperty("逆行预警 0关闭 1开启")
    private Integer retrogradeWarning;
    
    @ApiModelProperty("行星换座 0关闭 1开启")
    private Integer planetChange;
    
    @ApiModelProperty("新月满月 0关闭 1开启")
    private Integer moonPhase;
    
    @ApiModelProperty("日月食 0关闭 1开启")
    private Integer eclipse;
    
    @ApiModelProperty("月亮空亡 0关闭 1开启")
    private Integer moonVoid;
    
    @ApiModelProperty("行星相位 0关闭 1开启")
    private Integer planetAspect;

    /**
     * 苹果内购有效期，如果苹果过期了，返回null
     * 不含额外奖励的vip时长
     */
    public LocalDateTime autoIapExpiryDateNullable() {
        LocalDateTime d = vipExpiryDate;
        if (d == null) {
            return null;
        }

        LocalDateTime today = LocalDateTime.now();
        if (d.isBefore(today)) {
            return null;
        }
        return d;
    }

    public LocalDateTime autoExpiryDateNullable() {
        LocalDateTime d = autoIapExpiryDateNullable();
        Integer h = SafeUtil.of(this.vipHours);
        if (d != null) {
            if (h > 0) {
                d = d.plusHours(h);
            }
        } else {
            d = rewardVipBeginDate;
            if (d != null && h > 0) {
                d = d.plusHours(h);
            }
        }
        if (d != null) {
            LocalDateTime today = LocalDateTime.now();
            if (d.isBefore(today)) {
                d = null;
            }
        }
        return d;
    }
}
