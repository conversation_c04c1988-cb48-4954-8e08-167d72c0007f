package com.lhx.birthday.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.request.astrolabe.*;
import com.lhx.birthday.service.IAstrolabeService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@RequestMapping("/astrolabe")
@RestController
@Slf4j
@Api(description = "app接口 - 星盘模块", tags = "AstrolabeController")
public class AstrolabeController {

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IAstrolabeService astrolabeService;

    @Autowired
    private CommonUtil commonUtil;
    
    // 用于存储用户分析请求的缓存，key为用户ID+请求参数的哈希值
    private static final Map<String, JSONObject> analysisCache = new ConcurrentHashMap<>();
    // 缓存分析结果的过期时间（毫秒），默认30分钟
    private static final long CACHE_EXPIRY_TIME = 30 * 60 * 1000;
    // 记录缓存创建时间
    private static final Map<String, Long> cacheCreationTime = new ConcurrentHashMap<>();
    
    // 添加一个标志来追踪每个请求的缓存状态，key是缓存键，value为true表示该缓存正在生成中
    private static final Map<String, Boolean> cacheGenerationInProgress = new ConcurrentHashMap<>();
    
    // 存储正在生成的分析结果以及订阅这些结果的所有发射器，用于实时广播到多个客户端
    private static final Map<String, CachedAnalysisStream> analysisStreams = new ConcurrentHashMap<>();
    
    /**
     * 缓存的分析流，包含到目前为止的内容和订阅的发射器列表
     */
    private static class CachedAnalysisStream {
        private final StringBuilder contentBuilder = new StringBuilder();
        private final CopyOnWriteArrayList<SseEmitter> subscribers = new CopyOnWriteArrayList<>();
        private long lastUpdateTime = System.currentTimeMillis();
        private boolean completed = false;
        
        public String getContent() {
            return contentBuilder.toString();
        }
        
        public void addSubscriber(SseEmitter emitter) {
            subscribers.add(emitter);
        }
        
        public void removeSubscriber(SseEmitter emitter) {
            subscribers.remove(emitter);
        }
        
        public boolean hasSubscribers() {
            return !subscribers.isEmpty();
        }
        
        public void complete() {
            completed = true;
            for (SseEmitter emitter : subscribers) {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.debug("完成发射器时发生异常", e);
                }
            }
            subscribers.clear();
        }
        
        public boolean isCompleted() {
            return completed;
        }
        
        public long getLastUpdateTime() {
            return lastUpdateTime;
        }
    }
    
    /**
     * 定期检查并清理分析流
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void cleanupAnalysisStreams() {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;
        
        for (Iterator<Map.Entry<String, CachedAnalysisStream>> it = analysisStreams.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, CachedAnalysisStream> entry = it.next();
            CachedAnalysisStream stream = entry.getValue();
            
            // 如果流已完成或者长时间未更新且没有订阅者，则移除
            if (stream.isCompleted() || 
                (currentTime - stream.getLastUpdateTime() > CACHE_EXPIRY_TIME && !stream.hasSubscribers())) {
                it.remove();
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            log.info("清理了 {} 个过期的分析流", cleanedCount);
        }
    }

    /**
     * 基础接口-新闻内容
     * @param articleDetailsRequest
     * @return
     */
    @ApiOperation("基础接口-新闻内容")
    @PostMapping("/article/details")
    public Result articleDetails(@RequestBody ArticleDetailsRequest articleDetailsRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.articleDetails(articleDetailsRequest);
    }

    /**
     * 星盘-本命盘
     * @param natalRequest
     * @return
     */
    @ApiOperation("星盘-本命盘")
    @PostMapping("/chart/natal")
    public Result chartNatal(@RequestBody NatalRequest natalRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.chartNatal(natalRequest);
    }

    @ApiOperation("星盘-本命盘语料")
    @PostMapping("/chart/natal/chat")
    public Result getNatalCorpus(@RequestBody NatalCorpusRequest natalCorpusRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.getNatalCorpus(natalCorpusRequest);
    }

    /**
     * 星盘-统一接口
     * @param chartRequest 请求参数
     * @return 星盘数据
     */
    @ApiOperation("星盘-统一接口")
    @PostMapping("/chart")
    public Result getChart(@RequestBody ChartRequest chartRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.getChart(chartRequest);
    }
    
    /**
     * 星盘-语料统一接口
     * @param chartRequest 请求参数
     * @return 星盘语料数据
     */
    @ApiOperation("星盘-语料统一接口")
    @PostMapping("/chart/corpus")
    public Result<JSONObject> getChartCorpus(@RequestBody ChartRequest chartRequest){
        int type = chartRequest.getType();

        if(chartRequest.getChartType()==2){
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            String day = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            chartRequest.setTransitday(day);
        }
        
        // 获取当天筛选后的语料用于接口返回
        Result<JSONObject> chartCorpus = astrolabeService.getChartCorpus(chartRequest);

        // 仅对行运盘进行昨日对比
        if(chartRequest.getChartType()==2){
            // 计算并对比昨日语料
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
            String day = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            chartRequest.setTransitday(day);
            chartRequest.setType(type);
            Result<JSONObject> oldChartCorpus = astrolabeService.getChartCorpus(chartRequest);
            
            // 处理今日和昨日语料对比
            processCorpusComparison(chartCorpus.getResult(), oldChartCorpus.getResult());
        }
        return chartCorpus;
    }
    
    /**
     * 处理今日和昨日语料对比
     * 对比corpus和corpus_sec数组，如果title字段前三个字一致但不完全相同，则视为一组
     * @param todayResult 今日结果
     * @param yesterdayResult 昨日结果
     */
    private void processCorpusComparison(JSONObject todayResult, JSONObject yesterdayResult) {
        JSONArray corpusToday = todayResult.getJSONArray("corpus");
        JSONArray corpusYesterday = yesterdayResult.getJSONArray("corpus");
        JSONArray matchedTitleGroups = new JSONArray();

        if (corpusToday != null && corpusYesterday != null) {
            for (Object todayObj : corpusToday) {
                if (todayObj instanceof JSONObject) {
                    JSONObject todayItem = (JSONObject) todayObj;
                    String todayTitle = todayItem.getString("title");
                    if (todayTitle != null && todayTitle.length() >= 3) {
                        String todayTitlePrefix = todayTitle.substring(0, 3);
                        for (Object yesterdayObj : corpusYesterday) {
                            if (yesterdayObj instanceof JSONObject) {
                                JSONObject yesterdayItem = (JSONObject) yesterdayObj;
                                String yesterdayTitle = yesterdayItem.getString("title");
                                if (yesterdayTitle != null && yesterdayTitle.length() >= 3) {
                                    String yesterdayTitlePrefix = yesterdayTitle.substring(0, 3);
                                    // 新增判断：前3字相同但title不完全相同才匹配
                                    if (todayTitlePrefix.equals(yesterdayTitlePrefix) 
                                        && !todayTitle.equals(yesterdayTitle)) { 
                                        JSONObject matchedGroup = new JSONObject();
                                        matchedGroup.put("today_title", todayTitle);
                                        matchedGroup.put("yesterday_title", yesterdayTitle);
                                        matchedGroup.put("today_data", todayItem.clone());
                                        matchedTitleGroups.add(matchedGroup);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        todayResult.put("matched_title_groups", matchedTitleGroups);
        todayResult.put("corpus_sec", corpusYesterday);
    }

    /**
     * 星盘-年运报告
     * @param yearReportRequest
     * @return
     */
    @ApiOperation("星盘-年运报告")
    @PostMapping("/year/report")
    public Result report(@RequestBody YearReportRequest yearReportRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.report(yearReportRequest);
    }

    /**
     * 星盘-语料列表
     * @param corpusconstellationRequest
     * @return
     */
    @ApiOperation("星盘-语料列表")
    @PostMapping("/corpusconstellation/getList")
    public Result corpusconstellationList(@RequestBody CorpusconstellationRequest corpusconstellationRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.corpusconstellationList(corpusconstellationRequest);
    }

    /**
     * 星盘语料-语料
     * @param luckRequest
     * @return
     */
    @ApiOperation("星盘语料-语料")
    @PostMapping("/luck")
    public Result luck(@RequestBody LuckRequest luckRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.luck(luckRequest);
    }

    /**
     * 星盘语料-缘分合盘
     * @param evaluationcombinationRequest
     * @return
     */
    @ApiOperation("星盘语料-缘分合盘")
    @PostMapping("/evaluationcombination")
    public Result evaluationcombination(@RequestBody EvaluationcombinationRequest evaluationcombinationRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return astrolabeService.evaluationcombination(evaluationcombinationRequest);
    }

    /**
     * 星相日历接口
     * @param astroCalendarRequest 请求参数，包含年份和月份，或具体日期
     * @return 星相日历数据
     */
    @ApiOperation("星相日历接口 - 支持按月查询或按天查询")
    @PostMapping("/astrocalendar")
    public Result getAstroCalendar(@RequestBody AstroCalendarRequest astroCalendarRequest){
        return astrolabeService.getAstroCalendar(astroCalendarRequest);
    }
    
    /**
     * 获取行星逆顺事件对
     * @param planetRetrogradePairsRequest 请求参数，包含行星代号和日期
     * @return 行星逆顺事件对数据
     */
    @ApiOperation("获取行星逆顺事件对 - 根据行星代号查询逆行-顺行事件对")
    @PostMapping("/planet/retrograde/pairs")
    public Result getPlanetRetrogradePairs(@RequestBody PlanetRetrogradePairsRequest planetRetrogradePairsRequest){
        return astrolabeService.getPlanetRetrogradePairs(planetRetrogradePairsRequest);
    }

    /**
     * 推算星座
     * @param signPushRequest
     * @return
     */
    @ApiOperation("推算星座")
    @PostMapping("/sign/push")
    public Result signPush(@RequestBody SignPushRequest signPushRequest){
        return astrolabeService.signPush(signPushRequest);
    }

    /**
     * 获取星体信息
     * @param starInfoByIdRequest 根据id获取星体信息请求
     * @return 星体信息
     */
    @ApiOperation("获取星体信息")
    @PostMapping("/star/info")
    public Result getStarInfo(@RequestBody StarInfoByIdRequest starInfoByIdRequest) {
        return astrolabeService.getStarInfoById(starInfoByIdRequest);
    }

    @ApiOperation("星盘-AI分析")
    @PostMapping(value = "/chart/analysis")
    public SseEmitter getChartAnalysis(@RequestBody ChartRequest chartRequest) {
        SseEmitter emitter = new SseEmitter(0L);

        try {
            // 获取用户id
            String customerId = commonUtil.getOperatorId();
            
            // 调整行运日期 - 必须在生成缓存key之前执行
        if (chartRequest.getChartType() == 2) {
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            String day = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            chartRequest.setTransitday(day);
        }

            // 生成缓存key，基于用户ID和请求参数
            String cacheKey = generateCacheKey(customerId, chartRequest);
            log.info("用户ID: {}, 生成缓存键: {}", customerId, cacheKey);
            
            // 检查缓存是否过期
            checkAndCleanExpiredCache(cacheKey);
            
            // 获取缓存的分析结果
            JSONObject cachedResult = analysisCache.get(cacheKey);
            
            // 如果存在完整缓存的分析结果，发送缓存的内容
            if (cachedResult != null && cachedResult.containsKey("content")) {
                log.info("用户ID: {}, 命中完整缓存，直接返回缓存结果", customerId);
                astrolabeService.sendCachedAnalysisStream(cachedResult, emitter);
                return emitter;
            }
            
            // 检查是否有正在进行的分析流
            CachedAnalysisStream analysisStream = analysisStreams.get(cacheKey);
            if (analysisStream != null && !analysisStream.isCompleted()) {
                log.info("用户ID: {}, 发现正在进行的分析流，订阅实时更新", customerId);
                
                // 先发送到目前为止累积的内容
                String currentContent = analysisStream.getContent();
                if (!currentContent.isEmpty()) {
                    try {
                        // 发送一个特殊事件，表示这是初始内容
                        emitter.send(SseEmitter.event()
//                            .name("initial_content")
                            .data(currentContent));
                    } catch (IOException e) {
                        log.error("发送累积内容失败", e);
                        emitter.complete();
                        return emitter;
                    }
                }
                
                // 然后添加为订阅者以接收后续更新
                analysisStream.addSubscriber(emitter);
                
                // 设置回调以在客户端断开连接时移除订阅
                emitter.onCompletion(() -> {
                    analysisStream.removeSubscriber(emitter);
                    log.info("用户ID: {}, 从分析流中移除订阅", customerId);
                });
                
                return emitter;
            }
            
            // 检查是否有其他请求正在为此缓存键生成内容
            if (cacheGenerationInProgress.getOrDefault(cacheKey, false)) {
                log.info("用户ID: {}, 缓存正在生成但尚未有流数据，创建新的流", customerId);
                
                // 创建新的分析流
                CachedAnalysisStream newStream = new CachedAnalysisStream();
                newStream.addSubscriber(emitter);
                analysisStreams.put(cacheKey, newStream);
                
                // 设置回调以在客户端断开连接时移除订阅
                emitter.onCompletion(() -> {
                    newStream.removeSubscriber(emitter);
                    log.info("用户ID: {}, 从分析流中移除订阅", customerId);
                });
                
                return emitter;
            }
            
            // 获取星盘数据
        Result<JSONObject> fullChartResult = astrolabeService.getChart(chartRequest);
        if (!fullChartResult.isSuccess()) {
            try {
                emitter.send(SseEmitter.event().name("error").data("获取星盘数据失败，无法进行AI分析"));
            } catch (IOException e) {
                log.error("Error sending SSE error message", e);
            } finally {
                emitter.complete();
            }
            return emitter;
        }

            // 没有缓存，标记正在生成中
            cacheGenerationInProgress.put(cacheKey, true);
            
            // 创建新的分析流
            CachedAnalysisStream newStream = new CachedAnalysisStream();
            newStream.addSubscriber(emitter);
            analysisStreams.put(cacheKey, newStream);
            
            // 设置回调以在客户端断开连接时移除订阅
            emitter.onCompletion(() -> {
                newStream.removeSubscriber(emitter);
                log.info("用户ID: {}, 从分析流中移除订阅", customerId);
            });
            
            // 创建新的分析请求
            JSONObject chartData = fullChartResult.getResult();
            
            log.info("用户ID: {}, 缓存未命中，开始调用AI分析", customerId);
            
            // 使用新方法获取分析结果并缓存 - 第一次请求直接发送到emitter，不经过分析流广播
            astrolabeService.getDeepSeekAnalysisStream(chartData, emitter, 
                // 内容更新回调
                contentUpdate -> {
                    try {
                        if (contentUpdate.containsKey("delta")) {
                            String delta = contentUpdate.getString("delta");
                            if (!StringUtils.isEmpty(delta)) {
                                // 更新分析流 - 只添加新的增量内容
                                CachedAnalysisStream stream = analysisStreams.get(cacheKey);
                                if (stream != null) {
                                    // 对于第一个请求，内容已经直接发送到emitter
                                    // 这里只需要更新内容，不需要广播
                                    stream.contentBuilder.append(delta);
                                    stream.lastUpdateTime = System.currentTimeMillis();
                                    
                                    // 只向其他订阅者广播，不包括第一个请求的emitter
                                    for (Iterator<SseEmitter> it = stream.subscribers.iterator(); it.hasNext();) {
                                        SseEmitter subscriber = it.next();
                                        if (subscriber != emitter) { // 跳过第一个请求的emitter
                                            try {
                                                subscriber.send(SseEmitter.event().data(delta));
                                            } catch (Exception e) {
                                                // 如果发送失败，移除订阅者
                                                it.remove();
                                                try {
                                                    subscriber.complete();
                                                } catch (Exception ex) {
                                                    log.debug("完成发射器时发生异常", ex);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理内容更新失败", e);
                    }
                },
                // 完整结果回调
                result -> {
                    try {
                        if (result != null && result.containsKey("content")) {
                            String content = result.getString("content");
                            log.info("用户ID: {}, AI分析完成，缓存结果，内容长度: {}", customerId, content.length());
                            
                            // 直接存入缓存
                            analysisCache.put(cacheKey, result);
                            cacheCreationTime.put(cacheKey, System.currentTimeMillis());
                            log.info("用户ID: {}, 缓存已成功写入, 键: {}", customerId, cacheKey);
                            
                            // 标记分析流为完成状态
                            CachedAnalysisStream stream = analysisStreams.get(cacheKey);
                            if (stream != null) {
                                stream.complete();
                            }
                        }
                    } finally {
                        // 无论成功与否，都要移除生成中标记
                        cacheGenerationInProgress.remove(cacheKey);
                    }
                }
            );

        return emitter;
        } catch (Exception e) {
            log.error("处理星盘AI分析请求异常", e);
            try {
                emitter.send(SseEmitter.event().name("error").data("处理请求时发生异常: " + e.getMessage()));
            } catch (IOException ex) {
                log.error("Error sending SSE error message", ex);
            } finally {
                emitter.complete();
            }
            return emitter;
        }
    }
    
    /**
     * 生成缓存键
     * @param userId 用户ID
     * @param chartRequest 请求参数
     * @return 缓存键
     */
    private String generateCacheKey(String userId, ChartRequest chartRequest) {
        StringBuilder sb = new StringBuilder(userId);
        sb.append("_").append(chartRequest.getChartType());
        sb.append("_").append(chartRequest.getType() != null ? chartRequest.getType() : "null");
        
        if (chartRequest.getConstellationId() != null) {
            sb.append("_").append(chartRequest.getConstellationId());
        }
        
        if (chartRequest.getConstellationIds() != null && !chartRequest.getConstellationIds().isEmpty()) {
            sb.append("_").append(String.join("_", chartRequest.getConstellationIds().toString()));
        }
        
        if (chartRequest.getTransitday() != null) {
            sb.append("_").append(chartRequest.getTransitday());
        }
        
        // 添加当前日期，确保每天都会重新分析
        sb.append("_").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        
        return String.valueOf(sb.toString().hashCode());
    }
    
    /**
     * 检查并清理过期的缓存
     * @param cacheKey 当前请求的缓存键
     */
    private void checkAndCleanExpiredCache(String cacheKey) {
        Long creationTime = cacheCreationTime.get(cacheKey);
        if (creationTime != null) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - creationTime > CACHE_EXPIRY_TIME) {
                log.info("缓存已过期，移除缓存: {}", cacheKey);
                analysisCache.remove(cacheKey);
                cacheCreationTime.remove(cacheKey);
                cacheGenerationInProgress.remove(cacheKey);
                analysisStreams.remove(cacheKey);
            }
        }
        
        // 顺便清理其他过期缓存，但不要在每次请求时都执行完整清理
        // 使用概率方式执行清理，减少性能影响
        if (Math.random() < 0.1) { // 10%的概率执行清理
            int cleanedCount = 0;
            long currentTime = System.currentTimeMillis();
            
            for (Iterator<Map.Entry<String, Long>> it = cacheCreationTime.entrySet().iterator(); it.hasNext();) {
                Map.Entry<String, Long> entry = it.next();
                if (currentTime - entry.getValue() > CACHE_EXPIRY_TIME) {
                    analysisCache.remove(entry.getKey());
                    cacheGenerationInProgress.remove(entry.getKey());
                    analysisStreams.remove(entry.getKey());
                    it.remove();
                    cleanedCount++;
                }
            }
            
            if (cleanedCount > 0) {
                log.info("执行定期缓存清理，共清理 {} 个过期缓存项", cleanedCount);
            }
        }
    }
}
