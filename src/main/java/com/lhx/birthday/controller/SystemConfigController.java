package com.lhx.birthday.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.config.CacheConfig;
import com.lhx.birthday.response.systemConfig.CarouselConfigResponse;
import com.lhx.birthday.response.systemConfig.ContactUsConfigResponse;
import com.lhx.birthday.response.systemConfig.SmsSourceConfigResponse;
import com.lhx.birthday.response.systemConfig.SuccessConfigResponse;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.service.impl.SystemConfigServiceImpl;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.*;
import com.lhx.birthday.vo.systemConfig.*;
import com.tyme.eightchar.EightChar;
import com.tyme.solar.SolarTime;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static com.lhx.birthday.constant.SystemConfigConstant.*;

/**
 * <AUTHOR>
 */
@RequestMapping("/system")
@RestController
@Slf4j
@Api(description = "app接口 - 系统设置", tags = "SystemConfigController")
public class SystemConfigController {

    @Autowired
    private SystemConfigServiceImpl systemConfigService;

    @Resource
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    @Resource
    private CacheConfig cacheConfig;

    /**
     * 择吉日配置
     * @return
     */
    @ApiOperation("择吉日配置")
    @GetMapping("/zeshi")
    public Result zeshi(){
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_ZESHI_CONFIG_TYPE);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        return Result.OK(jsonObject);
    }

    /**
     * 选择国家和地区(未登录)
     * @return
     */
    @ApiOperation("选择国家和地区")
    @GetMapping("/sms-countries")
    public Result<SmsSourceConfigResponse> smsCountries(){
        List<SmsSourceVO> smsSourceVOList = new ArrayList<>();
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(SMS_CONFIG_KEY, SMS_CONFIG_TYPE);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        JSONArray sourceArray = jsonObject.getJSONArray("source");
        for (int i = 0; i < sourceArray.size(); i++) {
            JSONObject sourceObject = sourceArray.getJSONObject(i);
            SmsSourceVO smsSourceVO = SmsSourceVO.builder()
                    .chn(sourceObject.getString("chn"))
                    .eng(sourceObject.getString("eng"))
                    .countryCode(sourceObject.getString("countryCode"))
                    .code(sourceObject.getString("code"))
                    .build();
            smsSourceVOList.add(smsSourceVO);
        }
        return Result.OK(SmsSourceConfigResponse.builder()
                .smsSources(smsSourceVOList)
                .build());
    }

    /**
     * 联系我们
     * @return
     */
    @ApiOperation("联系我们")
    @GetMapping("/contact-us")
    public Result<ContactUsConfigResponse> contactUs(){
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_CONTACT_US_CONFIG_TYPE);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        return Result.OK(ContactUsConfigResponse.builder()
                .contactUs(ContactUsVO.builder()
                        .imgUrl(jsonObject.getString("imgurl"))
                        .build())
                .build());
    }

    /**
     * 排盘字色
     * @return
     */
    @ApiOperation(value = "排盘字色")
    @RequestMapping(value = "/font-color", method = RequestMethod.GET)
    public String fontColor() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_FONT_COLOR_CONFIG_TYPE);
        return systemConfig.getContext();
    }

    /**
     * 生肖配置
     * @return
     */
    @ApiOperation(value = "生肖配置")
    @RequestMapping(value = "/zodiac", method = RequestMethod.GET)
    public String zodiac() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_ZODIAC_CONFIG_TYPE);
        return systemConfig.getContext();
    }

    /**
     * 星座配置
     * @return
     */
    @ApiOperation(value = "星座配置")
    @RequestMapping(value = "/constellation", method = RequestMethod.GET)
    public String constellation() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_CONSTELLATION_CONFIG_TYPE);
        return systemConfig.getContext();
    }

    /**
     * 星盘配置
     * @return
     */
    @ApiOperation(value = "星盘配置")
    @RequestMapping(value = "/astrolabe", method = RequestMethod.GET)
    public String astrolabe() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_XINGPAN_CONFIG_TYPE);
        return systemConfig.getContext();
    }

    /**
     * 会员成功案例
     * @return
     */
    @ApiOperation(value = "会员成功案例")
    @RequestMapping(value = "/success", method = RequestMethod.GET)
    public Result<SuccessConfigResponse> success() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_SUCCESS);
        JSONArray jsonArray = JSONObject.parseObject(systemConfig.getContext()).getJSONArray("source");
        List<SuccessSourceVO> sourceVOS = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            sourceVOS.add(SuccessSourceVO.builder()
                    .icon(jsonObject.getString("icon"))
                    .name(jsonObject.getString("name"))
                    .member(jsonObject.getString("member"))
                    .role(jsonObject.getString("role"))
                    .content(jsonObject.getString("content"))
                    .build());
        }
        return Result.OK(SuccessConfigResponse.builder()
                .success(sourceVOS)
                .build());
    }

    /**
     * 走马灯
     * @return
     */
    @ApiOperation(value = "走马灯")
    @RequestMapping(value = "/carousel", method = RequestMethod.GET)
    public Result<CarouselConfigResponse> carousel() {
        CarouselConfigResponse cachedData = (CarouselConfigResponse) cacheConfig.getLongCachedData("carousel");
        if (cachedData != null) {
            return Result.OK(cachedData);
        }

        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_CAROUSEL);
        JSONArray jsonArray = JSONObject.parseObject(systemConfig.getContext()).getJSONArray("source");
        List<CarouselSourceVO> sourceVOS = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            sourceVOS.add(CarouselSourceVO.builder()
                    .phone(jsonObject.getString("phone"))
                    .name(jsonObject.getString("name"))
                    .time(getRandomTimeAgo())
                    .build());
        }
        CarouselConfigResponse configResponse = CarouselConfigResponse.builder()
                .carousel(sourceVOS)
                .build();
        cacheConfig.putLongCachedData("carousel",configResponse);
        return Result.OK(configResponse);
    }

    /**
     * 系统配置
     * @return
     */
    @ApiOperation(value = "系统配置")
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    public Result setting() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, SYSTEM_SETTING);
        return Result.OK(JSONObject.parseObject(systemConfig.getContext()));
    }

    @ApiOperation(value = "时区配置")
    @RequestMapping(value = "/timeZone", method = RequestMethod.GET)
    public Result timeZone() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, TIMEZONE_CONFIG_TYPE);
        return Result.OK(JSONObject.parseObject(systemConfig.getContext()));
    }

    /**
     * 星座小图标
     * @return
     */
    @ApiOperation(value = "星座小图标")
    @RequestMapping(value = "/constellationSmall", method = RequestMethod.GET)
    public Result eightChar() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_CONSTELLATION_SMALL);
        return Result.OK(JSONObject.parseObject(systemConfig.getContext()));
    }

    public static String getRandomTimeAgo() {
        Random random = new Random();

        // 生成0到59之间的随机分钟数
        int minutesAgo = random.nextInt(60);

        // 生成0到23之间的随机小时数
        int hoursAgo = random.nextInt(24);

        // 组合时间单位与随机生成的时间值
        if (random.nextBoolean()) { // 随机决定返回小时还是分钟
            return hoursAgo + "小时前";
        } else {
            return minutesAgo + "分钟前";
        }
    }

}
