package com.lhx.birthday.controller;

import com.lhx.birthday.entity.Personality;
import com.lhx.birthday.entity.PersonalityTestQuestion;
import com.lhx.birthday.entity.PersonalityTestResult;
import com.lhx.birthday.request.personality.PersonalityTestRequest;
import com.lhx.birthday.service.IPersonalityTestService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.PersonalityQuestionVO;
import com.lhx.birthday.vo.PersonalityResultVO;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 性格测试Controller
 * <AUTHOR> lhx
 */
@RestController
@RequestMapping("/api/personality")
@Slf4j
public class PersonalityController {

    @Resource
    private IPersonalityTestService personalityTestService;
    
    @Resource
    private CommonUtil commonUtil;

    /**
     * 获取所有题目
     * 
     * @return 题目列表（按照questionId排序）
     */
    @GetMapping("/questions")
    public Result<List<PersonalityQuestionVO>> getAllQuestions() {
        try {
            Map<Integer, List<PersonalityTestQuestion>> questionMap = personalityTestService.getAllQuestions();
            
            // 转换为前端需要的格式
            List<PersonalityQuestionVO> questionVOList = new ArrayList<>();
            
            for (Map.Entry<Integer, List<PersonalityTestQuestion>> entry : questionMap.entrySet()) {
                Integer questionId = entry.getKey();
                List<PersonalityTestQuestion> options = entry.getValue();
                
                // 确保所有选项都有相同的问题文本
                String questionText = options.get(0).getQuestionText();
                
                // 构建选项列表
                List<PersonalityQuestionVO.Option> optionList = options.stream()
                        .sorted(Comparator.comparing(PersonalityTestQuestion::getOptionLetter))
                        .map(opt -> PersonalityQuestionVO.Option.builder()
                                .optionLetter(opt.getOptionLetter())
                                .optionText(opt.getOptionText())
                                .build())
                        .collect(Collectors.toList());
                
                // 创建问题VO
                PersonalityQuestionVO questionVO = PersonalityQuestionVO.builder()
                        .questionId(questionId)
                        .questionText(questionText)
                        .options(optionList)
                        .build();
                
                questionVOList.add(questionVO);
            }
            
            // 按照问题ID排序
            questionVOList.sort(Comparator.comparing(PersonalityQuestionVO::getQuestionId));
            
            return Result.OK(questionVOList);
        } catch (Exception e) {
            log.error("获取题目列表失败", e);
            return Result.error("获取题目列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 提交测试答案并获取测试结果
     * 
     * @param request 测试请求
     * @return 测试结果
     */
    @PostMapping("/submit")
    public Result<PersonalityResultVO> submitTest(@RequestBody PersonalityTestRequest request) {
        try {
            // 从CommonUtil获取用户ID
            Long userId = Long.parseLong(commonUtil.getOperatorId());
            
            // 保存测试结果
            PersonalityTestResult testResult = personalityTestService.saveTestResult(
                    userId, request.getAnswers());
            
            // 获取对应的性格类型详情
            Personality personality = personalityTestService.getPersonalityByTypeCode(testResult.getTypeCode());
            
            // 转换为VO
            PersonalityResultVO resultVO = PersonalityResultVO.from(testResult, personality);
            
            return Result.OK(resultVO);
        } catch (Exception e) {
            log.error("提交测试失败", e);
            return Result.error("提交测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户的最新测试结果
     * 
     * @return 最新的测试结果
     */
    @GetMapping("/result")
    public Result<PersonalityResultVO> getUserLatestResult() {
        try {
            // 从CommonUtil获取用户ID
            Long userId = Long.parseLong(commonUtil.getOperatorId());
            
            PersonalityTestResult testResult = personalityTestService.getUserLatestResult(userId);
            
            if (testResult == null) {
                return Result.error("用户尚未进行测试");
            }
            
            // 获取对应的性格类型详情
            Personality personality = personalityTestService.getPersonalityByTypeCode(testResult.getTypeCode());
            
            // 转换为VO
            PersonalityResultVO resultVO = PersonalityResultVO.from(testResult, personality);
            
            return Result.OK(resultVO);
        } catch (Exception e) {
            log.error("获取用户测试结果失败", e);
            return Result.error("获取用户测试结果失败：" + e.getMessage());
        }
    }
} 