package com.lhx.birthday.job.userinfo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.entity.AstroCalendarEvent;
import com.lhx.birthday.entity.CorpusAstro;
import com.lhx.birthday.entity.Push;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.mapper.AstroCalendarEventMapper;
import com.lhx.birthday.mapper.CorpusAstroMapper;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.service.IJPushService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.lhx.birthday.job.birthday.PushJobHandler.createPush;

@Component
@Slf4j
@JobHandler(value = "SecPushJobHandler")
public class SecPushJobHandler extends IJobHandler {

    @Autowired
    private RedisService redisService;

    @Autowired
    private IJPushService pushService;
    
    @Autowired
    private AstroCalendarEventMapper astroCalendarEventMapper;
    
    @Autowired
    private CorpusAstroMapper corpusAstroMapper;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            String time = LocalTime.now().format(formatter);
            
            // 处理星座每日运势推送 - 按星座分组推送
            processZodiacHoroscope(time);
            
            // 处理其他类型推送
            processTypeEvents(time);
            
        } catch (Exception e) {
            log.error("推送任务执行异常", e);
            e.printStackTrace();
        }
        return SUCCESS;
    }
    
    /**
     * 处理星座每日运势推送 - 按星座分组推送
     */
    private void processZodiacHoroscope(String time) throws Exception {
        // 遍历所有星座类型
        for (int zodiacIndex = 0; zodiacIndex < 12; zodiacIndex++) {
            try {
                // 构建星座特定的Redis键
                String key = RedisKeyConstant.USER_PUSH_XINGZUO + ":" + zodiacIndex;
                
                // 获取此时间点需要接收该星座推送的用户ID列表
                String userIdsStr = redisService.hget(key, time);
                if (StringUtils.isBlank(userIdsStr)) {
                    continue;
                }
                
                // 解析用户ID列表
                JSONArray userArray = JSONArray.parseArray(userIdsStr);
                if (userArray == null || userArray.isEmpty()) {
                    continue;
                }
                
                // 转换为字符串列表
                List<String> userIds = new ArrayList<>();
                for (int i = 0; i < userArray.size(); i++) {
                    userIds.add(String.valueOf(userArray.getInteger(i)));
                }
                
                if (userIds.isEmpty()) {
                    continue;
                }
                
                // 获取对应星座的运势信息
                ZodiacSignType zodiacSignType = ZodiacSignType.fromValue(zodiacIndex);
                String zodiacName = zodiacSignType.getValue();
                
                JSONObject infoObject = getZodiacInfoObject(zodiacIndex);
                if (infoObject != null) {
                    JSONObject obj = infoObject.getJSONObject("今日运势");
                    if (obj != null) {
                        String title = zodiacName + "今日运势已生成";
                        String alert = buildZodiacAlert(obj);
                        Push push = createPush(title, alert, "tabbar", "1");
                        
                        sendPush(push, userIds);
                        log.info("已为{}个{}用户推送星座运势", userIds.size(), zodiacName);
                    }
                }
            } catch (Exception e) {
                log.error("处理星座{}运势推送异常: {}", ZodiacSignType.fromValue(zodiacIndex).getValue(), e.getMessage(), e);
            }
        }
    }
    
    /**
     * 获取星座运势信息
     */
    private JSONObject getZodiacInfoObject(int index) throws Exception {
        String infoKey = RedisKeyConstant.HOROSCOPE_KEY + "1:" + index;
        String data = redisService.getString(infoKey);
        if (StringUtils.isNotBlank(data)) {
            return JSONObject.parseObject(data);
        }
        return null;
    }
    
    /**
     * 构建星座运势推送内容
     */
    private String buildZodiacAlert(JSONObject obj) {
        return obj.getString("今明运势");
    }
    
    /**
     * 处理天象日历事件推送
     */
    private void processTypeEvents(String time) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = now.withSecond(0).withNano(0);
            LocalDateTime endTime = startTime.plusMinutes(1).minusNanos(1);
            
            // 查询当前分钟内的天象事件
            List<AstroCalendarEvent> events = astroCalendarEventMapper.findAllByEventDatetimeBetween(startTime, endTime);
            
            if (events != null && !events.isEmpty()) {
                log.info("查询到{}个天象事件需要推送", events.size());
                
                // 根据事件类型进行分组
                Map<Integer, List<AstroCalendarEvent>> eventsByType = events.stream()
                        .collect(Collectors.groupingBy(AstroCalendarEvent::getType));
                
                // 获取所有事件的type和title用于查询语料库
                Map<String, String> sentenceMap = getSentenceMap(events);
                
                // 处理逆行预警 type=4
                processEventsByPushType(eventsByType.get(4), RedisKeyConstant.USER_PUSH_RETROGRADE_WARNING, sentenceMap);
                
                // 处理行星换座 type=2
                processEventsByPushType(eventsByType.get(2), RedisKeyConstant.USER_PUSH_PLANET_CHANGE, sentenceMap);
                
                // 处理新月满月 type=6或9
                List<AstroCalendarEvent> moonPhaseEvents = new ArrayList<>();
                if (eventsByType.containsKey(6)) {
                    moonPhaseEvents.addAll(eventsByType.get(6));
                }
                if (eventsByType.containsKey(9)) {
                    moonPhaseEvents.addAll(eventsByType.get(9));
                }
                processEventsByPushType(moonPhaseEvents, RedisKeyConstant.USER_PUSH_MOON_PHASE, sentenceMap);
                
                // 处理日月食 type=15, 19, 20, 25
                List<AstroCalendarEvent> eclipseEvents = new ArrayList<>();
                for (Integer type : Arrays.asList(15, 19, 20, 25)) {
                    if (eventsByType.containsKey(type)) {
                        eclipseEvents.addAll(eventsByType.get(type));
                    }
                }
                processEventsByPushType(eclipseEvents, RedisKeyConstant.USER_PUSH_ECLIPSE, sentenceMap);
                
                // 处理月亮空亡 type=13, 14
                List<AstroCalendarEvent> moonVoidEvents = new ArrayList<>();
                if (eventsByType.containsKey(13)) {
                    moonVoidEvents.addAll(eventsByType.get(13));
                }
                if (eventsByType.containsKey(14)) {
                    moonVoidEvents.addAll(eventsByType.get(14));
                }
                processEventsByPushType(moonVoidEvents, RedisKeyConstant.USER_PUSH_MOON_VOID, sentenceMap);
                
                // 处理行星相位 type=1
                processEventsByPushType(eventsByType.get(1), RedisKeyConstant.USER_PUSH_PLANET_ASPECT, sentenceMap);
            }
        } catch (Exception e) {
            log.error("处理天象事件推送异常", e);
        }
    }
    
    /**
     * 按推送类型处理事件
     */
    private void processEventsByPushType(List<AstroCalendarEvent> events, String pushKey, Map<String, String> sentenceMap) {
        if (events == null || events.isEmpty()) {
            return;
        }
        
        // 获取需要接收此类推送的用户ID列表
        String userIdsStr = redisService.hget(pushKey, "all");
        if (StringUtils.isBlank(userIdsStr)) {
            return;
        }
        
        List<String> pushIds = getIdList(userIdsStr);
        
        if (pushIds.isEmpty()) {
            return;
        }
        
        for (AstroCalendarEvent event : events) {
            try {
                // 构建推送内容
                String eventTime = event.getEventTime();
                String title = event.getTitle();
                String typeTitle = eventTime + " " + title;
                
                // 获取推送内容
                String suggest = sentenceMap.getOrDefault(event.getType() + ":" + title, "");
                
                // 创建推送对象
                Push push = createPush(typeTitle, suggest, "tabbar", "1");
                
                // 发送推送
                sendPush(push, pushIds);
                log.info("已为{}个用户推送天象事件: {}", pushIds.size(), title);
            } catch (Exception e) {
                log.error("推送天象事件异常: " + event.getTitle(), e);
            }
        }
    }
    
    /**
     * 获取事件对应的语料
     */
    private Map<String, String> getSentenceMap(List<AstroCalendarEvent> events) {
        Map<String, String> sentenceMap = new HashMap<>();
        
        try {
            // 提取唯一的 (type, title) 组合
            Set<Map.Entry<Integer, String>> uniquePairs = events.stream()
                    .filter(e -> e.getType() != null && StringUtils.isNotBlank(e.getTitle()))
                    .map(e -> new AbstractMap.SimpleEntry<>(e.getType(), e.getTitle()))
                    .collect(Collectors.toSet());
            
            if (!uniquePairs.isEmpty()) {
                // 批量查询语料
                for (Map.Entry<Integer, String> pair : uniquePairs) {
                    Optional<CorpusAstro> corpusAstroOptional = corpusAstroMapper.findByTypeAndTitle(pair.getKey(), pair.getValue());
                    if (corpusAstroOptional.isPresent()) {
                        CorpusAstro corpusAstro = corpusAstroOptional.get();
                        JSONObject jsonObject = JSONObject.parseObject(corpusAstro.getChartContentJson());
                        String sentence = jsonObject.getString("suggest");
                        if (StringUtils.isNotBlank(sentence)) {
                            sentenceMap.put(pair.getKey() + ":" + pair.getValue(), sentence);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取事件语料异常", e);
        }
        
        return sentenceMap;
    }

    /**
     * 解析用户ID列表
     */
    private List<String> getIdList(String jsonString) {
        List<String> idList = new ArrayList<>();
        try {
            JSONArray array = JSONArray.parseArray(jsonString);
            for (int j = 0; j < array.size(); j++) {
                idList.add(String.valueOf(array.getInteger(j)));
            }
        } catch (Exception e) {
            log.error("解析用户ID列表异常", e);
        }
        return idList;
    }

    /**
     * 发送推送
     */
    private void sendPush(Push push, List<String> pushIds) {
        if (pushIds.isEmpty()) {
            return;
        }
        
        Iterator<String> iterator = pushIds.iterator();
        while (iterator.hasNext()) {
            List<String> subList = new ArrayList<>();
            for (int k = 0; k < 1000 && iterator.hasNext(); k++) {
                subList.add(iterator.next());
            }
            // iOS 推送
            pushService.pushIos(push, subList.toArray(new String[0]));
            
            // Android 推送需要处理长度限制
            push.setAlert(processString(push.getAlert()));
            pushService.pushAndroid(push, subList.toArray(new String[0]));
        }
    }

    /**
     * 处理推送文本，确保Android推送长度合适
     */
    public static String processString(String input) {
        final int maxLength = 50;
        if (input == null) {
            return "";
        }
        
        if (input.length() > maxLength) {
            // 如果字符串过长，则截取前50个字符，并在末尾加上省略号
            return input.substring(0, maxLength - 3) + "...";
        } else if (input.length() < maxLength) {
            // 如果字符串不够长，在末尾填充空格
            StringBuilder sb = new StringBuilder(input);
            while (sb.length() < maxLength) {
                sb.append(" ");
            }
            return sb.toString();
        } else {
            // 如果字符串正好是50个字符，直接返回
            return input;
        }
    }
}
