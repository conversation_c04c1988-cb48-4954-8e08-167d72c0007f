package com.lhx.birthday.job.userinfo;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

import java.io.IOException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import java.security.PrivateKey;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.entity.UserPaySubscribe;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.mapper.UserPaySubscribeMapper;
import com.lhx.birthday.service.impl.TvMemberRechargeTypeServiceImpl;
import com.lhx.birthday.service.impl.UserInfoServiceImpl;
import com.lhx.birthday.service.impl.UserPaySubscribeServiceImpl;
import com.lhx.birthday.util.FeishuBotClient;
import com.lhx.birthday.vo.UserInfoVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.lhx.birthday.constant.SettingConstant.IS_VIP;
import static com.lhx.birthday.constant.SettingConstant.NOT_VIP;


/**
 * 检测会员过期定时任务
 */
@Component
@Slf4j
@JobHandler(value = "CheckOrderJobHandler")
public class CheckOrderJobHandler extends IJobHandler {

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private UserInfoServiceImpl userInfoService;

    @Autowired
    private UserPaySubscribeMapper userPaySubscribeMapper;

    @Autowired
    private UserPaySubscribeServiceImpl userPaySubscribeService;

    private String  webhook = "fb2bc745-1209-4e88-a106-3836702c8785";

    @Autowired
    private TvMemberRechargeTypeServiceImpl tvMemberRechargeTypeService;


    private static final String JWT_KEY = "-----BEGIN PRIVATE KEY-----\n"
            + "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgY1kwixXgtJui1AUb\n"
            + "RTAWxw+HmwSxgiPLRIEChRMG2a2gCgYIKoZIzj0DAQehRANCAATy8CEtX5Mka2kP\n"
            + "SchuR1B4wYe1lDWb6WzvWb9kHHT7U5m9VywFk6YMkoiaJHfTWRBeE/gRlL/96AIp\n"
            + "e6ktiH50\n"
            + "-----END PRIVATE KEY-----";

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            String token = generateJwtToken();
            for (UserPaySubscribe userPaySubscribe : userPaySubscribeMapper.findAllOrder()) {
                String transactionId = userPaySubscribe.getOriginalTransactionId();
                if(userPaySubscribe.getProductId().contains("forever")){
                    Set<String> refundTid = getRefundTid(transactionId);
                    if(refundTid.contains(transactionId)){
                        userPaySubscribe.setStatus(5);
                        userPaySubscribe.setCancelDate(LocalDateTime.now());
                        userPaySubscribeMapper.saveAndFlush(userPaySubscribe);
                        //移除用户vip
                        UserInfo userInfo = userInfoMapper.findById(userPaySubscribe.getUserId()).get();
                        userInfo.setVip(NOT_VIP);
                        if(Objects.nonNull(userInfo.getCdKeyExpiryDate()) && Objects.nonNull(userInfo.getVipExpiryDate())){
                            if(userInfo.getCdKeyExpiryDate().isAfter(userInfo.getVipExpiryDate())){
                                userInfo.setVip(IS_VIP);
                                userInfo.setVipExpiryDate(null);
                            }
                        }
                        userInfoMapper.save(userInfo);
                    }
                }else{

                    String url = "https://api.storekit.itunes.apple.com/inApps/v1/subscriptions/" + transactionId;
                    // 构建请求参数

                    // 发送POST请求
                    HttpResponse response = HttpRequest.get(url)
                            .header("Authorization", "Bearer " + token)
                            .timeout(5000)
                            .execute();

                    // 读取响应结果
                    String result = response.body();
                    JSONObject jsonObject = JSONObject.parseObject(result);

                    // 检查是否存在errorCode
                    if (!jsonObject.containsKey("errorCode")) {
                        JSONArray dataArray = jsonObject.getJSONArray("data");
                        if (dataArray.size() > 0) {
                            JSONObject dataObj = dataArray.getJSONObject(0);
                            JSONArray lastTransactions = dataObj.getJSONArray("lastTransactions");
                            if (lastTransactions.size() > 0) {
                                JSONObject lastTransaction = lastTransactions.getJSONObject(0);
                                int status = lastTransaction.getIntValue("status");
                                boolean isCancel = status == 5;

                                if (isCancel) {
                                    Set<String> refundTid = getRefundTid(transactionId);
                                    for (UserPaySubscribe subscribe : userPaySubscribeMapper.findListByOriginalTransactionId(transactionId)) {
                                        if(refundTid.contains(subscribe.getTransactionId())){
                                            // 更新为取消状态，并设置取消时间
                                            subscribe.setStatus(status);
                                            subscribe.setCancelDate(LocalDateTime.now());
                                            userPaySubscribeMapper.saveAndFlush(subscribe);
                                        }else{
                                            subscribe.setStatus(2);
                                            userPaySubscribeMapper.saveAndFlush(subscribe);
                                        }
                                    }
                                    //移除用户vip
                                    UserInfo userInfo = userInfoMapper.findById(userPaySubscribe.getUserId()).get();
                                    userInfo.setVip(NOT_VIP);
                                    if(Objects.nonNull(userInfo.getCdKeyExpiryDate()) && Objects.nonNull(userInfo.getVipExpiryDate())){
                                        if(userInfo.getCdKeyExpiryDate().isAfter(userInfo.getVipExpiryDate())){
                                            userInfo.setVip(IS_VIP);
                                            userInfo.setVipExpiryDate(null);
                                        }
                                    }
                                    userInfoMapper.save(userInfo);
                                } else {
                                    String signedTransactionInfo = lastTransaction.getString("signedTransactionInfo");
                                    String signedRenewalInfo = lastTransaction.getString("signedRenewalInfo");

//                                userPaySubscribe.setSignedTransactionInfo(appleDecode(signedTransactionInfo));
//                                userPaySubscribe.setSignedRenewalInfo(appleDecode(signedRenewalInfo));
                                    Integer autoRenewStatus = JSONObject.parseObject(appleDecode(signedRenewalInfo)).getInteger("autoRenewStatus");
                                    userPaySubscribe.setAutoRenewStatus(autoRenewStatus);
                                    JSONObject object = JSONObject.parseObject(appleDecode(signedTransactionInfo));
                                    String tid = object.getString("transactionId");
                                    UserPaySubscribe paySubscribe = userPaySubscribeMapper.findByTransactionId(tid);
                                    if(Objects.isNull(paySubscribe)){
                                        JSONObject convertedJson = camelToUnderscore(object);
                                        convertedJson.put("expires_date_ms",convertedJson.getString("expires_date"));
                                        convertedJson.put("original_purchase_date_ms",convertedJson.getString("original_purchase_date"));
                                        convertedJson.put("purchase_date_ms",convertedJson.getString("purchase_date"));
                                        String originalTransactionId = convertedJson.getString("original_transaction_id");
                                        UserInfoVO userInfoVO = userInfoService.getByUserId(userPaySubscribeMapper.findByOriginalTransactionId(originalTransactionId).getUserId());
                                        if(Objects.nonNull(userInfoVO)){
                                            addSubscribe(convertedJson, userInfoVO, convertedJson.getString("environment"));
                                            addHistory(convertedJson, userInfoVO, convertedJson.getString("environment"));
                                        }
                                    }

                                    // 更新状态但不设置取消时间
                                    userPaySubscribe.setStatus(status);
                                    userPaySubscribeMapper.saveAndFlush(userPaySubscribe);
                                }
                            }
                        }
                    }

                    response.close();
                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return SUCCESS;
    }

    private void addHistory(JSONObject jsonObject, UserInfoVO user, String environment) {
        if ("Production".equals(environment)) {
            if(jsonObject.containsKey("receipt")){
                jsonObject.getJSONObject("receipt").getJSONArray("in_app").forEach(item -> {
                    JSONObject json = (JSONObject) item;
                    String transactionId = json.getString("transaction_id");
                    if (json.containsKey("expires_date")) {
                        UserPaySubscribe userPayModel = userPaySubscribeService.getByTransactionId(transactionId);
                        if (userPayModel == null) {
                            UserPaySubscribe model = paySubscribeModel(json, user.getUserId());
                            userPaySubscribeService.addUserPaySubscribe(model);
                        }
                    }
                });
            }else{
                String transactionId = jsonObject.getString("transaction_id");
                if (jsonObject.containsKey("expires_date")) {
                    UserPaySubscribe userPayModel = userPaySubscribeService.getByTransactionId(transactionId);
                    if (userPayModel == null) {
                        UserPaySubscribe model = paySubscribeModel(jsonObject, user.getUserId());
                        userPaySubscribeService.addUserPaySubscribe(model);
                    }
                }
            }
        }
    }

    private boolean addSubscribe(JSONObject first, UserInfoVO user, String environment) {
        UserPaySubscribe userPaySubscribe = userPaySubscribeService.getByTransactionId(first.getString("transaction_id"));
        String productId = first.getString("product_id");
        TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(productId);
        user.setVipExpiryDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(first.getLong("expires_date_ms")), ZoneId.systemDefault()));
        user.setVipProductId(tvMemberRechargeType != null ? tvMemberRechargeType.getCode() : null);
        // 判断是否需要更新兑换时长
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime rewardVipBeginDate = user.getRewardVipBeginDate();
        if (rewardVipBeginDate == null) {
            rewardVipBeginDate = now;
        }
        if(rewardVipBeginDate.isAfter(now)){
            Duration durationUntilBeginDate = Duration.between(now, rewardVipBeginDate);
            // 获取相差的分钟数
            long minutesUntilBeginDate = durationUntilBeginDate.toMinutes();
            user.setRewardVipBeginDate(user.getVipExpiryDate().plusMinutes(minutesUntilBeginDate));
            // 更新用户信息
            userInfoService.updateRewardVipBeginDate(user.getUserId(),user.getRewardVipBeginDate());
        }
        Integer vip = IS_VIP;
        if(user.getVipExpiryDate().isBefore(LocalDateTime.now())){
            vip = NOT_VIP;
        }
        userInfoService.updateVipExpireDate(user.getUserId(), user.getVipExpiryDate() ,user.getVipProductId(), vip);
        // 判断试用逻辑
        assert tvMemberRechargeType != null;
        if(tvMemberRechargeType.getAppleFreeDay()>0){
            userInfoService.updateFreeUse(user.getUserId());
        }
        if (userPaySubscribe == null) {
            if ("Production".equals(environment)) {
                UserPaySubscribe model = paySubscribeModel(first, user.getUserId());
                userPaySubscribeService.addUserPaySubscribe(model);
                try {
                    FeishuBotClient.sendMessage(webhook, "星愿助手用户:"+ model.getUserId()+"续订会员:" + model.getProductId());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return true;
        }
        return false;
    }

    private UserPaySubscribe paySubscribeModel(JSONObject item, Long userId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        UserPaySubscribe userPaySubscribe = UserPaySubscribe.builder()
                .userId(userId)
                .expiresDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(item.getLong("expires_date_ms")), ZoneId.systemDefault()).format(formatter))
                .expiresDateMs(item.getString("expires_date_ms"))
                .inAppOwnershipType(item.getString("in_app_ownership_type"))
                .isInIntroOfferPeriod(item.getString("is_in_intro_offer_period"))
                .isTrialPeriod(item.getString("is_trial_period"))
                .originalPurchaseDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(item.getLong("original_purchase_date_ms")), ZoneId.systemDefault()).format(formatter))
                .originalPurchaseDateMs(item.getString("original_purchase_date_ms"))
                .originalTransactionId(item.getString("original_transaction_id"))
                .productId(item.getString("product_id"))
                .purchaseDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(item.getLong("purchase_date_ms")), ZoneId.systemDefault()).format(formatter))
                .purchaseDateMs(item.getString("purchase_date_ms"))
                .quantity(item.getString("quantity"))
                .subscriptionGroupIdentifier(item.getString("subscription_group_identifier"))
                .transactionId(item.getString("transaction_id"))
                .webOrderLineItemId(item.getString("web_order_line_item_id"))
                .createDate(LocalDateTime.now())
                .build();
        if(Objects.isNull(userPaySubscribe.getIsTrialPeriod())){
            userPaySubscribe.setIsTrialPeriod("false");
        }
        return userPaySubscribe;
    }

    public static JSONObject camelToUnderscore(JSONObject json) {
        if (json == null) {
            return null;
        }

        JSONObject newJson = new JSONObject();
        for(Map.Entry<String, Object> entry : json.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String newKey = camelToUnderscoreKey(key);
            newJson.put(newKey, convertValue(value));
        }
        return newJson;
    }

    private static String camelToUnderscoreKey(String key) {
        StringBuilder underscoreKey = new StringBuilder();
        for (int i = 0; i < key.length(); i++) {
            char c = key.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    underscoreKey.append('_');
                }
                underscoreKey.append(Character.toLowerCase(c));
            } else {
                underscoreKey.append(c);
            }
        }
        return underscoreKey.toString();
    }

    private static Object convertValue(Object value) {
        if (value instanceof JSONObject) {
            return camelToUnderscore((JSONObject) value);
        } else if (value instanceof JSONArray) {
            JSONArray array = (JSONArray) value;
            JSONArray newArray = new JSONArray();
            for (Object item : array) {
                newArray.add(convertValue(item));
            }
            return newArray;
        }
        return value;
    }

    private static String generateJwtToken() {
        String token = "";
        try {
            String privateKeyPEM = JWT_KEY.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");

            byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);

            KeyFactory kf = KeyFactory.getInstance("EC");
            PKCS8EncodedKeySpec keySpecPKCS8 = new PKCS8EncodedKeySpec(encoded);
            PrivateKey privateKey = kf.generatePrivate(keySpecPKCS8);

            // 设置JWT的Header和Payload
            Map<String, Object> headers = new HashMap<>();
            headers.put("alg", "ES256");
            headers.put("kid", "845NW4G9UG");
            headers.put("typ", "JWT");

            Map<String, Object> claims = new HashMap<>();
            claims.put("iss", "21c97657-c3dc-4690-ac8a-30847aa4d3c5");
            claims.put("aud", "appstoreconnect-v1");
            claims.put("iat", new Date().getTime() / 1000); // iat in seconds
            claims.put("exp", (new Date().getTime() / 1000) + 60 * 60); // exp in seconds
            claims.put("bid", "com.wykj.fortune");

            // 生成JWT
            token = Jwts.builder()
                    .setClaims(claims)
                    .setHeader(headers)
                    .signWith(SignatureAlgorithm.ES256, privateKey)
                    .compact();
        } catch (Exception e){
            e.printStackTrace();
        }
        return token;
    }

    private static Map<String, Object> header() {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("alg", "ES256");
        headerMap.put("kid", "845NW4G9UG");
        headerMap.put("typ", "JWT");
        return headerMap;
    }

    private static Set<String> getRefundTid(String transactionId){
        Set<String> tids = new HashSet<>();
        try {
            String token = generateJwtToken();
            String url = "https://api.storekit.itunes.apple.com/inApps/v2/refund/lookup/" + transactionId;
            // 构建请求参数

            // 发送POST请求
            HttpResponse response = HttpRequest.get(url)
                    .header("Authorization", "Bearer " + token)
                    .timeout(5000)
                    .execute();

            // 读取响应结果
            String result = response.body();
            JSONObject jsonObject = JSONObject.parseObject(result);
            for (Object transactions : jsonObject.getJSONArray("signedTransactions")) {
                JSONObject info = JSONObject.parseObject(appleDecode(transactions.toString()));
                String tId = info.getString("transactionId");
                tids.add(tId);
            }
            response.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return tids;
    }

    public static void main(String[] args) {
        try {
            String token = generateJwtToken();
            String transactionId = "540001699054202";
            String url = "https://api.storekit.itunes.apple.com/inApps/v2/refund/lookup/" + transactionId;
            // 构建请求参数

            // 发送POST请求
            HttpResponse response = HttpRequest.get(url)
                    .header("Authorization", "Bearer " + token)
                    .timeout(5000)
                    .execute();

            // 读取响应结果
            String result = response.body();
            JSONObject jsonObject = JSONObject.parseObject(result);
            System.out.printf("123");
            System.out.println(jsonObject);
            System.out.println(appleDecode(jsonObject.getString("signedTransactions")));;
            // 检查是否存在errorCode
//            if (!jsonObject.containsKey("errorCode")) {
//                JSONArray dataArray = jsonObject.getJSONArray("data");
//                if (dataArray.size() > 0) {
//                    JSONObject dataObj = dataArray.getJSONObject(0);
//                    JSONArray lastTransactions = dataObj.getJSONArray("lastTransactions");
//                    if (lastTransactions.size() > 0) {
//                        JSONObject lastTransaction = lastTransactions.getJSONObject(0);
//                        System.out.println(lastTransaction);
//
//                        String signedTransactionInfo = lastTransaction.getString("signedTransactionInfo");
//                        String signedRenewalInfo = lastTransaction.getString("signedRenewalInfo");
//
//                        System.out.printf(String.valueOf(JSONObject.parseObject(appleDecode(signedTransactionInfo))));
//                        System.out.printf(String.valueOf(JSONObject.parseObject(appleDecode(signedRenewalInfo))));
//                       ;
////                        boolean isCancel = status == 5;
//
////                        if (isCancel) {
////
////                        } else {
////                            String signedTransactionInfo = lastTransaction.getString("signedTransactionInfo");
////                            System.out.println(appleDecode(signedTransactionInfo));
////                            String signedRenewalInfo = lastTransaction.getString("signedRenewalInfo");
////                            System.out.println(appleDecode(signedRenewalInfo));
////                        }
//                    }
//                }
//            }


            response.close();
        }catch (Exception e){
            e.printStackTrace();
        }

        /**
         *
         * accountTenure int 账号注册时间
         *      0 未声明
         *      1 1-3天
         *      2 3-10天
         *      3 10-30天
         *      4 30-90天
         *      5 90-180天
         *      6 180-365天
         *      7 超过365
         * appAccountToken uuid
         * consumptionStatus
         *      0 消费状况未公布。使用此值可以避免提供该字段的信息。
         *      1 应用内购买不会被消耗。
         *      2 应用内购买已部分消耗。
         *      3 应用内购买已完全消耗。
         * customerConsented true 表明客户是否同意提供消费数据。
         * deliveryStatus true 指示应用是否成功交付了正常运行的应用内购买。
         * lifetimeDollarsPurchased 表示客户在所有平台上在您的应用中进行的应用内购买的总金额（以美元为单位）。
         *      0 Lifetime purchase amount is undeclared. Use this value to avoid providing information for this field.
         *      1 Lifetime purchase amount is 0 USD.
         *      2 Lifetime purchase amount is between 0.01–49.99 USD.
         *      3 Lifetime purchase amount is between 50–99.99 USD.
         *      4 Lifetime purchase amount is between 100–499.99 USD.
         *      5 Lifetime purchase amount is between 500–999.99 USD.
         *      6 Lifetime purchase amount is between 1000–1999.99 USD.
         *      7 Lifetime purchase amount is over 2000 USD.
         * lifetimeDollarsRefunded 表示客户在您的应用程序中跨所有平台收到的退款总额（以美元为单位）。
         * platform
         *      0 未知
         *      1 苹果平台
         *      2 非苹果平台
         * playTime 使用平台的量
         *      0 The engagement time is undeclared. Use this value to avoid providing information for this field.
         *      1 The engagement time is between 0–5 minutes.
         *      2 The engagement time is between 5–60 minutes.
         *      3 The engagement time is between 1–6 hours.
         *      4 The engagement time is between 6–24 hours.
         *      5 The engagement time is between 1–4 days.
         *      6 The engagement time is between 4–16 days.
         *      7 The engagement time is over 16 days.
         *  refundPreference
         *      0 未声明
         *      1 同意
         *      2 拒绝
         *      3 ？？？
         *  sampleContentProvided boolean 是否提供免费试用
         *  userStatus
         *      0 帐户状态未声明。使用此值可以避免提供该字段的信息。
         *      1 客户的帐户处于活动状态。
         *      2 客户的帐户已被暂停。
         *      3 客户的帐户被终止。
         *      4 客户的帐户访问权限有限。
         */
//
//        try {
//            String token = generateJwtToken();
//            String transactionId = "***************";
//            // PUT {transactionId}
//            String url = "https://api.storekit.itunes.apple.com/inApps/v1/transactions/consumption/" + transactionId;
//            // 构建请求参数
//            JSONObject jsonObject =  new JSONObject();
//            jsonObject.put("accountTenure",1);
//            jsonObject.put("appAccountToken","72eefd4e-564c-49be-ac0b-0f04347cea0c");
//            jsonObject.put("consumptionStatus",2);
//            jsonObject.put("customerConsented",true);
//            jsonObject.put("deliveryStatus",0);
//            jsonObject.put("lifetimeDollarsPurchased",2);
//            jsonObject.put("lifetimeDollarsRefunded",2);
//            jsonObject.put("platform",1);
//            jsonObject.put("playTime",2);
//            jsonObject.put("refundPreference",2);
//            jsonObject.put("sampleContentProvided",false);
//            jsonObject.put("userStatus",0);
//            // 发送POST请求
//            HttpResponse response = HttpRequest.put(url)
//                    .header("Authorization", "Bearer " + token)
//                    .header("Content-Type", "application/json")
//                    .body(jsonObject.toJSONString())
//                    .timeout(5000)
//                    .execute();
//
//            // 读取响应结果
//            String result = response.body();
//            System.out.println(result);
//            JSONObject parsedObject = JSONObject.parseObject(result);
//            System.out.println(parsedObject);
//
//
//            response.close();
//        }catch (Exception e){
//            e.printStackTrace();
//        }
    }

    public static String appleDecode(String encodeStr) {
        DecodedJWT decode = JWT.decode(encodeStr);
        return new String(Base64.getDecoder().decode(decode.getPayload()));
    }

}
