package com.lhx.birthday.job.birthday;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.entity.Push;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.enums.ZodiacType;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.service.IJPushService;
import com.tyme.lunar.LunarDay;
import com.xkzhangsan.time.LunarDate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 用户数据推送
 */
@Component
@Slf4j
@JobHandler(value = "PushJobHandler")
public class PushJobHandler extends IJobHandler {

    @Autowired
    private RedisService redisService;

    @Autowired
    private IJPushService pushService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            sendPromptReminders();
        }catch (Exception e){
            e.printStackTrace();
        }
        return SUCCESS;
    }

    private Set<String> getIdSet(String jsonString) {
        Set<String> idSet = new HashSet<>();
        JSONArray array = JSONArray.parseArray(jsonString);
        for (int j = 0; j < array.size(); j++) {
            idSet.add(String.valueOf(array.getInteger(j)));
        }
        return idSet;
    }

    public static String processString(String input) {
        final int maxLength = 50;
        if (input.length() > maxLength) {
            // 如果字符串过长，则截取前50个字符，并在末尾加上省略号
            return input.substring(0, maxLength - 3) + "...";
        } else if (input.length() < maxLength) {
            // 如果字符串不够长，在末尾填充空格
            StringBuilder sb = new StringBuilder(input);
            while (sb.length() < maxLength) {
                sb.append(" ");
            }
            return sb.toString();
        } else {
            // 如果字符串正好是50个字符，直接返回
            return input;
        }
    }

    private void sendPush(Push push, List<String> pushIds) {
        Iterator<String> iterator = pushIds.iterator();
        while (iterator.hasNext()) {
            List<String> subList = new ArrayList<>();
            for (int k = 0; k < 1000 && iterator.hasNext(); k++) {
                subList.add(iterator.next());
            }
            pushService.pushIos(push, subList.toArray(new String[0]));
            push.setAlert(processString(push.getAlert()));
            pushService.pushAndroid(push, subList.toArray(new String[0]));
        }
    }

    private void sendPromptReminders() {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

        LunarDate lunarDate = LunarDate.now();
        String solarTime = LocalDate.now().format(dateFormatter) + ":" + timeFormatter.format(LocalTime.now());
        String lunarTime = lunarDate.getlYearCn() + "-" + lunarDate.getLeapMonthCn() + lunarDate.getlMonthCn() + "-" + lunarDate.getlDayCn() + ":" + timeFormatter.format(LocalTime.now());
        String title = "生日提醒";

        sendReminder(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_DAILY_PROMPT, title, "今天是xx公历生日");
        sendReminder(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_ONE_DAY_PROMPT, title, "距离xx公历生日还有1天");
        sendReminder(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THREE_DAY_PROMPT, title, "距离xx公历生日还有3天");
        sendReminder(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_SEVEN_DAY_PROMPT, title, "距离xx公历生日还有7天");
        sendReminder(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT, title, "距离xx公历生日还有15天");
        sendReminder(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THIRTY_DAY_PROMPT, title, "距离xx公历生日还有30天");

        sendReminder(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_DAILY_PROMPT, title, "今天是xx农历生日");
        sendReminder(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_ONE_DAY_PROMPT, title, "距离xx农历生日还有1天");
        sendReminder(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THREE_DAY_PROMPT, title, "距离xx农历生日还有3天");
        sendReminder(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_SEVEN_DAY_PROMPT, title, "距离xx农历生日还有7天");
        sendReminder(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT, title, "距离xx农历生日还有15天");
        sendReminder(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THIRTY_DAY_PROMPT, title, "距离xx农历生日还有30天");
    }

    private void sendReminder(String scheduleTime, String key, String title, String messagePrefix) {
        String reminderStr = redisService.hget(key, scheduleTime);
        if (Objects.nonNull(reminderStr)) {
            JSONArray jsonArray = JSONArray.parseArray(reminderStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String alert = messagePrefix.replace("xx",jsonObject.getString("msg"));
                Set<String> idSet = getIdSet(jsonObject.getJSONArray("id").toJSONString());
                List<String> pushIds = new ArrayList<>(idSet);
                Push push = createPush(title, alert, "tabbar", "2");
                sendPush(push, pushIds);
            }
        }
    }

    public static Push createPush(String title, String alert, String type,String link) {
        Push push = new Push();
        push.setTitle(title);
        push.setAlert(alert);
        Map<String, String> extras = new HashMap<>();
        extras.put("type", type);
        extras.put("link", link);
        extras.put("param", "{}");
        push.setExtras(extras);
        return push;
    }

}
