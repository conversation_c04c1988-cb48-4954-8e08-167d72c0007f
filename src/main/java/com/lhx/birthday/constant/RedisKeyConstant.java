package com.lhx.birthday.constant;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:30
 */
public final class RedisKeyConstant {

    private RedisKeyConstant(){

    }

    /**
     * DeepSeek星盘分析缓存
     */
    public static final String DEEPSEEK_ANALYSIS_KEY = "deepseek:analysis:";

    /**
     * 占卜 - 星座/生肖每日运势
     */
    public static final String HOROSCOPE_KEY = "horoscopeInfo:";

    /**
     * 占卜
     */
    public static final String DIVINATION_KEY = "divination:";

    /**
     * 灵签
     */
    public static final String ORACLEDRAWS_KEY = "oracledraws:";

    /**
     * 小六壬
     */
    public static final String XIAOLIUREN_KEY = "xiaoliuren:";

    /**
     * 摇卦
     */
    public static final String YAOGUA_KEY = "yaogua:";

    /**
     * 星座
     */
    public static final String XINGZUO_KEY = "xingzuo:";

    /**
     * 生肖
     */
    public static final String SHENGXIAO_KEY = "shengxiao:";
    
    /**
     * 星座每日运势推送
     */
    public static final String USER_PUSH_XINGZUO = "user:push:horoscope:remind";
    
    /**
     * 逆行预警推送
     */
    public static final String USER_PUSH_RETROGRADE_WARNING = "user:push:retrograde:warning";
    
    /**
     * 行星换座推送
     */
    public static final String USER_PUSH_PLANET_CHANGE = "user:push:planet:change";
    
    /**
     * 新月满月推送
     */
    public static final String USER_PUSH_MOON_PHASE = "user:push:moon:phase";
    
    /**
     * 日月食推送
     */
    public static final String USER_PUSH_ECLIPSE = "user:push:eclipse";
    
    /**
     * 月亮空亡推送
     */
    public static final String USER_PUSH_MOON_VOID = "user:push:moon:void";
    
    /**
     * 行星相位推送
     */
    public static final String USER_PUSH_PLANET_ASPECT = "user:push:planet:aspect";

    /**
     * 年月日key
     */
    public static final String DAY_KEY = "day:";
    public static final String MONTH_KEY = "month:";
    public static final String YEAR_KEY = "year:";

    /**
     * 星盘
     * 十二宫
     * 星座
     * 角度
     */
    public static final String XINGPAN_PLANT_ZODIADC_KEY = "xingpan:plant:zodiac:";

    public static final String XINGPAN_PLANT_CONSTELLATION_KEY = "xingpan:plant:constellation:";

    public static final String XINGPAN_PLANT_ALLOW_KEY = "xingpan:plant:allow:zodiac:";

    /**
     * 星盘类型
     */
    public static final String XINGPAN_TRANSIT_TYPE = "transit:";
    public static final String XINGPAN_COMBINATION_TYPE = "combination:";
    public static final String XINGPAN_THIRDPROGRESSED_TYPE = "thirdprogressed:";
    public static final String XINGPAN_SECONDARYLIMIT_TYPE = "secondarylimit:";
    public static final String XINGPAN_LUNARRETURN_TYPE = "lunarreturn:";
    public static final String XINGPAN_SOLARRETURN_TYPE = "solarreturn:";
    public static final String XINGPAN_SOLARARC_TYPE = "solararc:";
    public static final String XINGPAN_DEVELOPED_TYPE = "developed:";
    public static final String XINGPAN_SMALLLIMIT_TYPE = "smalllimit:";
    public static final String XINGPAN_NATALTWELVEPOINTER_TYPE = "nataltwelvepointer:";
    public static final String XINGPAN_NATALTHIRTEENPOINTER_TYPE = "natalthirteenpointer:";
    public static final String XINGPAN_CURRENT_TYPE = "current:";
    public static final String XINGPAN_COMPARISION_A_TYPE = "comparision_a:";
    public static final String XINGPAN_COMPARISION_B_TYPE = "comparision_b:";
    public static final String XINGPAN_COMPOSITETHIRPROGR_TYPE = "compositeThirprogr:";
    public static final String XINGPAN_COMPOSITESECONDARY_TYPE = "compositesecondary:";
    public static final String XINGPAN_MARKS_A_TYPE = "marks_a:";
    public static final String XINGPAN_MARKS_B_TYPE = "marks_b:";
    public static final String XINGPAN_MARKSTHIRPROGR_A_TYPE = "marksthirprogr_a:";
    public static final String XINGPAN_MARKSTHIRPROGR_B_TYPE = "marksthirprogr_b:";
    public static final String XINGPAN_MARKSSECPROGR_A_TYPE = "markssecprogr_a:";
    public static final String XINGPAN_MARKSSECPROGR_B_TYPE = "markssecprogr_b:";
    public static final String XINGPAN_TIMESMIDPOINT_TYPE = "timesmidpoint:";
    public static final String XINGPAN_TIMESMIDPOINTTHIRPROGR_TYPE = "timesmidpointthirprogr:";
    public static final String XINGPAN_TIMESMIDPOINTSECPROGR_TYPE = "timesmidpointsecprogr:";
    public static final String XINGPAN_CHART_CONTENT_MARKDOWN_TYPE = "markdown:";

    /**
     * 星盘数据缓存键
     */
    public static final String XINGPAN_CHART_DATA_KEY = "xingpan:chart:data:";

    /**
     * 地市数据
     */
    public static final String REGION_INFO_KEY = "region:info";

    /**
     * 公历
     * 当天提醒
     * 提前一天
     * 提前三天
     * 提前七天
     * 提前十五天
     * 提前三十天
     */
    public static final String USER_PUSH_SOLAR_DAILY_PROMPT = "user:push:solar:prompt:daily";
    public static final String USER_PUSH_SOLAR_ONE_DAY_PROMPT = "user:push:solar:prompt:one";
    public static final String USER_PUSH_SOLAR_THREE_DAY_PROMPT = "user:push:solar:prompt:three";
    public static final String USER_PUSH_SOLAR_SEVEN_DAY_PROMPT = "user:push:solar:prompt:seven";
    public static final String USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT = "user:push:solar:prompt:fifteen";
    public static final String USER_PUSH_SOLAR_THIRTY_DAY_PROMPT = "user:push:solar:prompt:thirty";

    /**
     * 农历
     * 当天提醒
     * 提前一天
     * 提前三天
     * 提前七天
     * 提前十五天
     * 提前三十天
     */
    public static final String USER_PUSH_LUNAR_DAILY_PROMPT = "user:push:lunar:prompt:daily";
    public static final String USER_PUSH_LUNAR_ONE_DAY_PROMPT = "user:push:lunar:prompt:one";
    public static final String USER_PUSH_LUNAR_THREE_DAY_PROMPT = "user:push:lunar:prompt:three";
    public static final String USER_PUSH_LUNAR_SEVEN_DAY_PROMPT = "user:push:lunar:prompt:seven";
    public static final String USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT = "user:push:lunar:prompt:fifteen";
    public static final String USER_PUSH_LUNAR_THIRTY_DAY_PROMPT = "user:push:lunar:prompt:thirty";
}
