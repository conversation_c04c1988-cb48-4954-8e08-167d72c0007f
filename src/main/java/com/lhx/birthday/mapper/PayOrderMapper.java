package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Attachment;
import com.lhx.birthday.entity.pay.PayOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface PayOrderMapper extends JpaRepository<PayOrder, Long>, JpaSpecificationExecutor<PayOrder>  {

    PayOrder findByOrderId(String orderId);

}
