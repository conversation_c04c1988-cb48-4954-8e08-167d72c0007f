package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "年运报告请求参数")
public class YearReportRequest {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别 (1男2女)")
    private String sex;

    @ApiModelProperty(value = "生日（2022-03-30 12:12:12)")
    private String birthday;

    @ApiModelProperty(value = "出生地经度 15.555")
    private String birthLongitude;

    @ApiModelProperty(value = "出生地纬度度 15.555")
    private String birthLatitude;

    @ApiModelProperty(value = "出生地时区 中国 8")
    private String birthTz;

    @ApiModelProperty(value = "现居地经度 15.555")
    private String livingLongitude;

    @ApiModelProperty(value = "现居地纬度度 15.555")
    private String livingLatitude;

    @ApiModelProperty(value = "现居地时区 中国 8")
    private String livingTz;
}
