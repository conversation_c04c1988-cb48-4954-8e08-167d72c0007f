package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "星盘语料请求参数")
public class LuckRequest {

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "时区")
    private String tz;

    @ApiModelProperty(value = "生日时间格式1999-10-17 21:00:00")
    private String birthday;

    @ApiModelProperty(value = "0:日运语料 1:周运语料 2:月运语料 3:年运语料")
    private Integer type;

}
