package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "语料列表请求参数")
public class CorpusconstellationRequest {

    @ApiModelProperty(value = "依照描述结构")
    private String fallInto;

    @ApiModelProperty(value = "星盘类型")
    private String chartType;

}
