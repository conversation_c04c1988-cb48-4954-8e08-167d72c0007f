package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "星相日历请求")
public class AstroCalendarRequest {

    @ApiModelProperty(value = "年月，格式 yyyy-MM", example = "2023-05")
    private String yearMonth;
    
    @ApiModelProperty(value = "具体日期，格式 yyyy-MM-dd，优先级高于yearMonth和dateRange", example = "2023-05-15")
    private String date;
} 