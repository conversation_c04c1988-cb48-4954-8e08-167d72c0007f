package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "本命盘语料请求参数")
public class NatalCorpusRequest {

    @ApiModelProperty(value = "星座档案id", required = true)
    private Long constellationId;

    @ApiModelProperty(value = "类型（0:行星 1:宫位 2:相位）", required = true)
    private Integer type;

    @ApiModelProperty(value = "星体id")
    private List<Object> planets;

    @ApiModelProperty(value = "小行星id")
    private List<Integer> planetXs;

    @ApiModelProperty(value = "虚星id")
    private List<Object> virtual;

    @ApiModelProperty(value = "宫位系统 默认p")
    private String hSys;

    @ApiModelProperty(value = "数组下标为度数，值为允许度 例如 phase[90]=2 表示刑相90 允许度为2度")
    private Map<Integer, Float> phase;
} 