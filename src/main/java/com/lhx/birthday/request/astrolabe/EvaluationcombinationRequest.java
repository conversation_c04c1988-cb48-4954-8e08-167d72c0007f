package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "缘分合盘请求参数")
public class EvaluationcombinationRequest {

    @ApiModelProperty(value = "星座档案ID列表，固定为两个档案ID")
    private List<Long> constellationIds;

    @ApiModelProperty(value = "1情侣关系2其他关系")
    private String type;

}
