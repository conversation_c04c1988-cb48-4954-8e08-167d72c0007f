package com.lhx.birthday.request.userinfo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.enums.ZodiacType;
import com.lhx.birthday.util.LocalDateDeserializer;
import com.lhx.birthday.util.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Enumerated;
import java.time.LocalDate;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "用户修改请求参数")
public class UserUpdateRequest {

    @ApiModelProperty(value = "头像地址")
    private String avatar;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(name = "出生日期")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate birthDate;

    /**
     * 星座
     */
    @ApiModelProperty(name = "星座")
    private Integer zodiacSignType;

    /**
     * 生肖
     */
    @ApiModelProperty(name = "生肖")
    private Integer zodiacType;

    @ApiModelProperty(value = "极光推送id")
    private String registrationId;
    
    @ApiModelProperty(value = "星座运势提醒 0关闭 1开启")
    private Integer horoscopeRemind;
    
    @ApiModelProperty(value = "提醒时间")
    private String remindTime;
    
    @ApiModelProperty(value = "逆行预警 0关闭 1开启")
    private Integer retrogradeWarning;
    
    @ApiModelProperty(value = "行星换座 0关闭 1开启")
    private Integer planetChange;
    
    @ApiModelProperty(value = "新月满月 0关闭 1开启")
    private Integer moonPhase;
    
    @ApiModelProperty(value = "日月食 0关闭 1开启")
    private Integer eclipse;
    
    @ApiModelProperty(value = "月亮空亡 0关闭 1开启")
    private Integer moonVoid;
    
    @ApiModelProperty(value = "行星相位 0关闭 1开启")
    private Integer planetAspect;
}
