server:
  port: ${PORT:8082}
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /birthday
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

spring:
  application:
    name: birthday
  shardingsphere:
    enabled: true
    datasource:
      names: master
      master:
        driver-class-name: com.mysql.cj.jdbc.Driver
        type: com.alibaba.druid.pool.DruidDataSource
        url: jdbc:mysql://${MYSQL_IP:************}:${MYSQL_PORT:13956}/horoscope_prod?characterEncoding=UTF-8&&zeroDateTimeBehavior=convertToNull&autoReconnect=true&failOverReadOnly=false&connectTimeout=0&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME:horoscope_rwu}
        password: ${MYSQL_PASSWORD:h46eaN6JoQxo0VIe}
        filters: stat
        maxActive: 20
        initialSize: 1
        maxWait: 60000
        minIdle: 1
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: select 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxOpenPreparedStatements: 20
        removeAbandoned: true
        removeAbandonedTimeout: 60
        validationInterval: 30000
    props:
      sql:
        show: false
  main:
    allow-bean-definition-overriding: true
  redis:
    host: ${REDIS_IP:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:}
    database: ${REDIS_DB:7}
    timeout: 5000
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 5
        max-wait: 10000
      shutdown-timeout: 5000
    compress:
      enabled: true
      threshold: 8192
      level: 6
      log-stats: true
      batch-size: 50
  servlet:
    multipart:
      maxFileSize: 10MB
      maxRequestSize: 100MB

  jpa:
    database: mysql
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        hbm2ddl:
          auto: update
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
#    show-sql: true
    open-in-view: false
  # loacl prod
  profiles:
    active: ${ACTIVE:local}
  mapper-locations: classpath:com/lhx/birthday/mapper/xml/*Mapper.xml
  type-aliases-package: com.lhx.se.db.order.model

redisson:
  url: ${REDISSON_URL:localhost:6379}
  database: ${REDISSON_DB:8}
  password: ${REDISSON_PWD:}

# Redis压缩配置
redis:
  compress:
    # 是否启用压缩
    enabled: true
    # 压缩阈值（字节），大于此值的数据会被压缩，默认8KB
    threshold: 8192
    # 压缩级别（1-9），1为最快压缩，9为最高压缩率，默认为6
    level: 6
    # 是否在日志中记录压缩统计信息
    log-stats: true
    # 批量操作时的批次大小
    batch-size: 50

swagger:
  contact:
    name: lhx
    url: http://www.xxx.com
    email: <EMAIL>
  title: test
  description: 如有接口描述不清晰, 请联系相应开发人员添加
  version: 1.0.0
  enable: true

# jwt配置
jwt:
  secret-key: eda1782204cf41efaca1e051ccc610be62acdcf24c09f011f343583c41cfb941f
  url-patterns: /**
#  url-patterns: /d
  excluded-urls: /user/tourists/login,/user/verifyCode/*,/followAddress/getDistrictList,/getSubscribeList,/system/sms-countries,/market/setting,/market/integral-product-list,/system/contact-us,/market/product,/system/zeshi,/gongju/laohuangli,/system/font-color,/system/zodiac,/system/constellation,/system/success,/system/carousel,/system/astrolabe,/system/setting,/notify,/operate/*,/wxPay/refundNotify,/wxPay/payNotify,/version/list,/aliPay/cert_notify_url,/aliPay/cert_return_url,/aliPay/notify_url,/aliPay/return_url

# 阿里云号码认证服务
ali:
  phone:
    access-key-secret: ${ALI_ACC_KEY_SECRET:******************************}
    access-key-id: ${ALI_ACC_KEY_ID:LTAI5t73uFTRARiA2mYp7qSX}
  sms:
    #文壮的账号
    code-chars: '0123456789'
    code-len: 6
    code-minutes: 5
    sign: ${CN_SIGN:无远科技}
    template-code: ${CN_TEMPLATE_CODE:SMS_236475032}
    access-key-id: ${ALI_SMS_ACC_KEY_ID:LTAI5tSgZwqrtegUeRc13kud}
    access-key-secret: ${ALI_SMS_ACC_KEY_SECRET:******************************}
    en-template-code: ${EN_TEMPLATE_CODE:SMS_267970469}
    en-sign: ${EN_SIGN:wuyuankeji}

# 苹果商店配置
appStore:
  password: ${APP_STORE:7140850917384723a212c72d808a0837}

#阿里云oss存储和大鱼短信秘钥配置
oss:
  sms:
    accessKey: ${OSS_SMS_ACC_KEY:LTAI5tSgZwqrtegUeRc13kud}
    secretKey: ${OSS_SMS_SEC_KEY:******************************}
  accessKey: ${OSS_ACC_KEY:LTAI5tGRiq43ULN2GFdMzaqV}
  secretKey: ${OSS_SEC_KEY:******************************}
  endpoint: ${OSS_END_POINT:oss-cn-beijing.aliyuncs.com}
  bucketName: ${OSS_BUCKET_NAME:bazipaipan}
  staticDomain: ${OSS_STATIC_DOMAIN:https://bazipaipan.oss-cn-beijing.aliyuncs.com}
  roleArn: ${ROLE_ARN:acs:ram::1490273317139630:role/aliyunosstokengeneratorrole}
  regionId: ${REGION_ID:cn-beijing}
  roleSessionName: ${ROLE_SESSION_NAME:turntext}

jpush:
  appKey: ${JPUSH_APP_KEY:130b2bee84d34f984cdbce7b}
  secretKey: ${JPUSH_SECRET_KEY:4f3425a2490680f86fce3f42}

app:
  market:
    point-expiry-month: 12
  code:
    name: Eight

xxl:
  job:
    logretentiondays: 7
    admin:
      addresses: ${XXL_JOB_ADDRESS:http://***************:8990/xxl-job-admin}
    executor:
      appname: ${XXL_JOB_APP_NAME:birthday}
      ip: ${IP:}
      port: ${XXL_JOB_EXECUTOR_PORT:9991}
      logpath: ./log/jobhandler
      logretentiondays: 7
    accessToken: ${XXL_JOB_ACCESS_TOKEN:b5f7ed32b2e24789bdce1308afcafebe}

# 星盘
xingpan:
  url: ${XINGPAN_URL:https://www.xingpan.vip/astrology}
  access_token: ${XINGPAN_TOKEN:989f888c4283e2cc2d8a5aa4af60932c}

# 缘分居
yuanfenju:
  appKey: ${YUANFENJU_APP_KEY:sEe2K4voxMG7FFjcTT0VJ9JNE}

init: ${INIT_FLAG:false}

# 百度AD推广
baidu:
  ad:
    prod:
      url: http://bdmkt-test.wuyuankeji.com:28085
    test:
      url: http://bdmkt.wuyuankeji.com:8085

operate:
  pwd: ${OPERATE_PWD:gzH0skq4nr1m6LPx}